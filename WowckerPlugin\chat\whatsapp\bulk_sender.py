"""
WhatsApp群发消息发送器
负责发送群发消息并监控发送状态
"""

import os
import json
import time
import logging
import threading
from typing import List, Callable, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class WhatsAppBulkSender:
    """WhatsApp群发消息发送器"""
    
    def __init__(self, login_manager):
        """
        初始化群发发送器
        
        Args:
            login_manager: WhatsApp登录管理器实例
        """
        self.login_manager = login_manager
        self.store_data_dir = login_manager.store_data_dir
        
        # 文件路径
        self.bulk_send_file = os.path.join(self.store_data_dir, "bulk_send_requests.json")
        self.bulk_status_file = os.path.join(self.store_data_dir, "bulk_send_status.json")
        
        # 状态管理
        self.is_sending = False
        self.should_monitor = False
        self.monitor_thread = None
        
        # 回调函数
        self.progress_callback = None
        self.completion_callback = None
        self.error_callback = None
        
        logger.info(f"初始化群发发送器: {self.store_data_dir}")
    
    def set_callbacks(self, progress_callback: Callable = None, 
                     completion_callback: Callable = None, 
                     error_callback: Callable = None):
        """
        设置回调函数
        
        Args:
            progress_callback: 进度回调 (progress: int, status: str, current_phone: str)
            completion_callback: 完成回调 (success: bool, sent_count: int, total_count: int, errors: List)
            error_callback: 错误回调 (error_message: str)
        """
        self.progress_callback = progress_callback
        self.completion_callback = completion_callback
        self.error_callback = error_callback
    
    def send_bulk_messages(self, phone_numbers: List[str], message: str, delay: int = 3000) -> bool:
        """
        发送群发消息
        
        Args:
            phone_numbers: 电话号码列表
            message: 消息内容
            delay: 发送间隔（毫秒）
            
        Returns:
            bool: 发送请求是否成功提交
        """
        # 详细检查登录状态
        is_logged_in = self.login_manager.check_login_status()
        logger.info(f"群发发送器检查登录状态: is_logged_in={is_logged_in}, manager.is_logged_in={self.login_manager.is_logged_in}")

        if not is_logged_in:
            error_msg = "WhatsApp未登录，无法发送消息"
            logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
        
        if self.is_sending:
            error_msg = "正在发送中，请等待当前发送完成"
            logger.warning(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
        
        if not phone_numbers:
            error_msg = "电话号码列表为空"
            logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
        
        if not message.strip():
            error_msg = "消息内容不能为空"
            logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
        
        try:
            logger.info(f"开始群发消息到 {len(phone_numbers)} 个号码")
            
            # 创建发送请求
            request_data = {
                "action": "send_bulk",
                "phone_numbers": phone_numbers,
                "message": message,
                "delay": delay,
                "timestamp": time.time()
            }
            
            # 写入请求文件
            logger.info(f"写入群发请求文件: {self.bulk_send_file}")
            logger.info(f"请求数据: {request_data}")

            with open(self.bulk_send_file, "w", encoding="utf-8") as f:
                json.dump(request_data, f, ensure_ascii=False, indent=2)

            # 验证文件是否写入成功
            if os.path.exists(self.bulk_send_file):
                with open(self.bulk_send_file, "r", encoding="utf-8") as f:
                    written_data = json.load(f)
                    logger.info(f"文件写入验证成功，内容: {written_data}")
            else:
                logger.error(f"群发请求文件写入失败: {self.bulk_send_file}")

            # 清空状态文件
            self._clear_status_file()
            
            # 开始监控发送状态
            self.is_sending = True
            self._start_status_monitor()
            
            logger.info("群发请求已提交，开始监控发送状态")
            return True
            
        except Exception as e:
            error_msg = f"提交群发请求时出错: {str(e)}"
            logger.exception(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
    
    def _clear_status_file(self):
        """清空状态文件"""
        try:
            with open(self.bulk_status_file, "w", encoding="utf-8") as f:
                json.dump({
                    "status": "idle",
                    "timestamp": time.time()
                }, f)
        except Exception as e:
            logger.error(f"清空状态文件时出错: {str(e)}")
    
    def _start_status_monitor(self):
        """启动状态监控线程"""
        self.should_monitor = True
        self.monitor_thread = threading.Thread(target=self._monitor_status)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("已启动群发状态监控线程")
    
    def _monitor_status(self):
        """监控发送状态"""
        last_status = None
        
        while self.should_monitor and self.is_sending:
            try:
                if os.path.exists(self.bulk_status_file):
                    with open(self.bulk_status_file, "r", encoding="utf-8") as f:
                        try:
                            status_data = json.load(f)
                            
                            # 只处理状态变化
                            current_status = status_data.get("status", "unknown")
                            if current_status != last_status:
                                self._handle_status_update(status_data)
                                last_status = current_status
                            
                        except json.JSONDecodeError:
                            # 文件可能正在被写入，跳过这次读取
                            pass
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"监控群发状态时出错: {str(e)}")
                time.sleep(5)
        
        logger.info("群发状态监控线程已停止")
    
    def _handle_status_update(self, status_data: Dict[str, Any]):
        """处理状态更新"""
        status = status_data.get("status", "unknown")
        
        if status == "sending":
            # 发送中
            progress = status_data.get("progress", 0)
            current_phone = status_data.get("current_phone", "")
            sent_count = status_data.get("sent_count", 0)
            total_count = status_data.get("total_count", 0)
            message = status_data.get("message", "")
            
            logger.info(f"发送进度: {progress}% ({sent_count}/{total_count}) - {current_phone}")
            
            if self.progress_callback:
                self.progress_callback(progress, message, current_phone)
        
        elif status == "completed":
            # 发送完成
            sent_count = status_data.get("sent_count", 0)
            total_count = status_data.get("total_count", 0)
            failed_count = status_data.get("failed_count", 0)
            errors = status_data.get("errors", [])
            
            logger.info(f"群发完成: 成功 {sent_count}/{total_count}, 失败 {failed_count}")
            
            self.is_sending = False
            self.should_monitor = False
            
            if self.completion_callback:
                self.completion_callback(True, sent_count, total_count, errors)
        
        elif status == "error":
            # 发送错误
            error_message = status_data.get("message", "未知错误")
            logger.error(f"群发过程中出错: {error_message}")
            
            self.is_sending = False
            self.should_monitor = False
            
            if self.error_callback:
                self.error_callback(error_message)
    
    def stop_sending(self) -> bool:
        """
        停止发送（通过清空请求文件实现）
        
        Returns:
            bool: 停止是否成功
        """
        try:
            logger.info("正在停止群发...")
            
            # 清空请求文件
            if os.path.exists(self.bulk_send_file):
                with open(self.bulk_send_file, "w", encoding="utf-8") as f:
                    json.dump({}, f)
            
            # 停止监控
            self.should_monitor = False
            self.is_sending = False
            
            logger.info("群发已停止")
            return True
            
        except Exception as e:
            logger.exception(f"停止群发时出错: {str(e)}")
            return False
    
    def get_sending_status(self) -> Dict[str, Any]:
        """
        获取当前发送状态
        
        Returns:
            Dict[str, Any]: 发送状态信息
        """
        try:
            if os.path.exists(self.bulk_status_file):
                with open(self.bulk_status_file, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"获取发送状态时出错: {str(e)}")
        
        return {
            "status": "unknown",
            "timestamp": time.time()
        }
    
    def is_sending_in_progress(self) -> bool:
        """
        检查是否正在发送中
        
        Returns:
            bool: 是否正在发送
        """
        return self.is_sending
    
    def get_last_send_result(self) -> Optional[Dict[str, Any]]:
        """
        获取最后一次发送结果
        
        Returns:
            Optional[Dict[str, Any]]: 发送结果
        """
        try:
            if os.path.exists(self.bulk_status_file):
                with open(self.bulk_status_file, "r", encoding="utf-8") as f:
                    status_data = json.load(f)
                    if status_data.get("status") == "completed":
                        return status_data
        except Exception as e:
            logger.error(f"获取发送结果时出错: {str(e)}")
        
        return None
    
    def clear_send_history(self):
        """清空发送历史"""
        try:
            self._clear_status_file()
            if os.path.exists(self.bulk_send_file):
                with open(self.bulk_send_file, "w", encoding="utf-8") as f:
                    json.dump({}, f)
            logger.info("发送历史已清空")
        except Exception as e:
            logger.error(f"清空发送历史时出错: {str(e)}")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if hasattr(self, 'should_monitor'):
                self.should_monitor = False
        except:
            pass
