# -*- coding: utf-8 -*-

"""
WhatsApp登录组件
用于群发获客页面的WhatsApp登录功能
"""

import os
import sys
import logging
from PySide6.QtCore import Qt, Signal, QTimer, QThread, pyqtSignal
from PySide6.QtGui import QFont, QPixmap
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QTextEdit, QMessageBox, QProgressBar
)

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
sys.path.insert(0, project_root)

try:
    from chat.whatsapp.bulk_login_manager import WhatsAppBulkLoginManager
except ImportError as e:
    logging.error(f"无法导入WhatsApp登录管理器: {e}")
    WhatsAppBulkLoginManager = None

logger = logging.getLogger(__name__)

class QRCodeDisplayWidget(QWidget):
    """QR码显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # QR码显示区域
        self.qr_display = QTextEdit()
        self.qr_display.setReadOnly(True)
        self.qr_display.setMaximumHeight(200)
        self.qr_display.setStyleSheet("""
            QTextEdit {
                background-color: #000000;
                color: #ffffff;
                font-family: 'Courier New', monospace;
                font-size: 8pt;
                border: 1px solid #3c4454;
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.qr_display)
        
        # 说明文字
        self.instruction_label = QLabel("请使用WhatsApp扫描上方二维码完成登录")
        self.instruction_label.setAlignment(Qt.AlignCenter)
        self.instruction_label.setStyleSheet("color: #a0a0a0; font-size: 11pt;")
        layout.addWidget(self.instruction_label)
        
    def display_qr_code(self, qr_code: str):
        """显示QR码"""
        if qr_code:
            # 生成ASCII QR码
            try:
                import qrcode
                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=1,
                    border=1,
                )
                qr.add_data(qr_code)
                qr.make(fit=True)
                
                # 生成ASCII艺术
                qr_ascii = ""
                modules = qr.modules
                for row in modules:
                    line = ""
                    for module in row:
                        line += "██" if module else "  "
                    qr_ascii += line + "\n"
                
                self.qr_display.setText(qr_ascii)
                
            except ImportError:
                # 如果没有qrcode库，显示文本
                self.qr_display.setText(f"QR码数据:\n{qr_code}")
        else:
            self.qr_display.setText("等待QR码生成...")
    
    def clear_qr_code(self):
        """清空QR码显示"""
        self.qr_display.clear()

class WhatsAppLoginWidget(QWidget):
    """WhatsApp登录组件"""
    
    # 信号
    login_status_changed = Signal(bool)  # 登录状态变化
    login_error = Signal(str)  # 登录错误
    
    def __init__(self, username: str = "default_user", store_name: str = "bulk_messaging", parent=None):
        super().__init__(parent)
        self.username = username
        self.store_name = store_name
        self.login_manager = None
        
        # 初始化登录管理器
        if WhatsAppBulkLoginManager:
            self.login_manager = WhatsAppBulkLoginManager(username, store_name)
            self.login_manager.set_callbacks(
                login_status_callback=self._on_login_status_changed,
                qr_code_callback=self._on_qr_code_received,
                error_callback=self._on_login_error
            )
        
        self.setup_ui()
        
        # 定时检查登录状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.check_login_status)
        self.status_timer.start(5000)  # 每5秒检查一次
        
        # 初始检查登录状态
        self.check_login_status()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("WhatsApp登录")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #fff; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_frame = QFrame()
        self.status_frame.setStyleSheet("""
            QFrame {
                background-color: #2c313c;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        status_layout = QVBoxLayout(self.status_frame)
        status_layout.setContentsMargins(15, 15, 15, 15)
        
        self.status_label = QLabel("检查登录状态中...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #f8f8f2; font-size: 12pt;")
        status_layout.addWidget(self.status_label)
        
        # 进度条（登录过程中显示）
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #3c4454;
                border-radius: 4px;
                background-color: #1b1e23;
                text-align: center;
                color: #f8f8f2;
            }
            QProgressBar::chunk {
                background-color: #5e7ce0;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(self.status_frame)
        
        # QR码显示区域（初始隐藏）
        self.qr_widget = QRCodeDisplayWidget()
        self.qr_widget.setVisible(False)
        layout.addWidget(self.qr_widget)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.login_btn = QPushButton("开始登录")
        self.login_btn.clicked.connect(self.start_login)
        self.login_btn.setStyleSheet("""
            QPushButton {
                background-color: #5e7ce0;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 11pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4a6bc8;
            }
            QPushButton:pressed {
                background-color: #3f5ba9;
            }
            QPushButton:disabled {
                background-color: #3c4454;
                color: #8a8a8a;
            }
        """)
        
        self.logout_btn = QPushButton("退出登录")
        self.logout_btn.clicked.connect(self.logout)
        self.logout_btn.setVisible(False)
        self.logout_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 11pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        
        button_layout.addWidget(self.login_btn)
        button_layout.addWidget(self.logout_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
    def start_login(self):
        """开始登录"""
        if not self.login_manager:
            QMessageBox.warning(self, "错误", "登录管理器未初始化")
            return
            
        if self.login_manager.is_logged_in:
            QMessageBox.information(self, "提示", "已经登录，无需重复登录")
            return
            
        if self.login_manager.is_logging_in:
            QMessageBox.information(self, "提示", "正在登录中，请等待")
            return
        
        # 更新界面状态
        self.status_label.setText("正在启动登录流程...")
        self.progress_bar.setVisible(True)
        self.login_btn.setEnabled(False)
        self.qr_widget.setVisible(False)
        
        # 启动登录
        success = self.login_manager.start_login()
        if not success:
            self.status_label.setText("启动登录失败")
            self.progress_bar.setVisible(False)
            self.login_btn.setEnabled(True)
            QMessageBox.warning(self, "错误", "启动登录失败，请检查网络连接")
    
    def logout(self):
        """退出登录"""
        if not self.login_manager:
            return
            
        success = self.login_manager.logout()
        if success:
            self.status_label.setText("已退出登录")
            self.progress_bar.setVisible(False)
            self.login_btn.setVisible(True)
            self.login_btn.setEnabled(True)
            self.logout_btn.setVisible(False)
            self.qr_widget.setVisible(False)
            self.qr_widget.clear_qr_code()
            
            # 发送状态变化信号
            self.login_status_changed.emit(False)
    
    def check_login_status(self):
        """检查登录状态"""
        if not self.login_manager:
            return
            
        is_logged_in = self.login_manager.check_login_status()
        
        if is_logged_in and not self.logout_btn.isVisible():
            # 登录成功
            self._on_login_status_changed(True)
    
    def _on_login_status_changed(self, is_logged_in: bool):
        """登录状态变化回调"""
        if is_logged_in:
            self.status_label.setText("✅ 登录成功")
            self.progress_bar.setVisible(False)
            self.login_btn.setVisible(False)
            self.logout_btn.setVisible(True)
            self.qr_widget.setVisible(False)
        else:
            self.status_label.setText("未登录")
            self.progress_bar.setVisible(False)
            self.login_btn.setVisible(True)
            self.login_btn.setEnabled(True)
            self.logout_btn.setVisible(False)
            self.qr_widget.setVisible(False)
        
        # 发送状态变化信号
        self.login_status_changed.emit(is_logged_in)
    
    def _on_qr_code_received(self, qr_code: str):
        """QR码接收回调"""
        self.status_label.setText("请扫描二维码登录")
        self.progress_bar.setVisible(False)
        self.qr_widget.setVisible(True)
        self.qr_widget.display_qr_code(qr_code)
    
    def _on_login_error(self, error_message: str):
        """登录错误回调"""
        self.status_label.setText(f"登录失败: {error_message}")
        self.progress_bar.setVisible(False)
        self.login_btn.setEnabled(True)
        self.qr_widget.setVisible(False)
        
        # 发送错误信号
        self.login_error.emit(error_message)
    
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        if not self.login_manager:
            return False
        return self.login_manager.check_login_status()
    
    def get_session_info(self) -> dict:
        """获取会话信息"""
        if not self.login_manager:
            return {}
        return self.login_manager.get_session_info()
