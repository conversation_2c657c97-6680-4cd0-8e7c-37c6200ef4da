# 群发获客功能说明

## 功能概述

群发获客功能是一个新增的标签页，位于左侧边栏，允许用户批量发送消息给多个电话号码，用于客户获取和营销推广。

## 功能特性

### 1. 用户界面
- **左侧边栏新增标签**: "群发获客"按钮，图标为发送图标
- **右侧面板布局**: 分为左右两个区域
  - **左侧区域**: 电话号码表格显示
  - **右侧区域**: 操作控制面板

### 2. 电话号码管理
- **电话号码表格**: 
  - 显示序号、电话号码、状态三列
  - 支持表格排序和选择
  - 自动调整列宽
  - 支持滚动显示大量号码

- **号码验证功能**:
  - 支持国际格式: `+86138****8888`
  - 支持国内格式: `138****8888`
  - 自动格式验证和清理
  - 重复号码检测

### 3. 消息输入
- **电话号码输入框**: 
  - 支持单个号码输入
  - 实时格式验证
  - 回车键快速添加
  
- **消息内容输入框**:
  - 多行文本输入
  - 支持长消息内容
  - 字符计数显示

### 4. 操作按钮
- **添加电话号码按钮**: 
  - 绿色按钮，添加验证通过的号码到列表
  - 自动清空输入框
  - 防重复添加

- **开始群发按钮**:
  - 橙色按钮，启动群发任务
  - 发送前确认对话框
  - 实时进度显示

## 技术实现

### 1. 文件结构
```
gui/
├── uis/
│   ├── pages/
│   │   └── ui_main_pages.py          # 新增page_9群发获客页面
│   └── windows/
│       └── main_window/
│           └── setup_main_window.py  # 新增左侧菜单按钮配置
├── core/
│   └── bulk_customer_acquisition_controller.py  # 群发获客控制器
└── main.py                           # 新增按钮点击处理逻辑
```

### 2. 核心组件

#### 控制器类 (BulkCustomerAcquisitionController)
- **电话号码验证**: 支持国内外格式验证
- **消息群发**: 多线程异步发送
- **进度跟踪**: 实时更新发送状态
- **错误处理**: 完善的异常处理机制

#### 工作线程 (BulkSendWorker)
- **异步发送**: 避免UI阻塞
- **进度回调**: 实时更新发送进度
- **可中断**: 支持取消发送任务

### 3. UI样式
- **深色主题**: 与应用整体风格一致
- **响应式布局**: 自适应窗口大小
- **现代化设计**: 圆角、阴影、渐变效果
- **交互反馈**: 悬停、点击状态变化

## 使用流程

### 1. 访问功能
1. 启动应用程序
2. 点击左侧边栏的"群发获客"按钮
3. 进入群发获客页面

### 2. 添加电话号码
1. 在"添加电话号码"输入框中输入号码
2. 支持格式：
   - 国际格式：`+8613812345678`
   - 国内格式：`13812345678`
3. 点击"添加电话号码"按钮或按回车键
4. 号码会显示在左侧表格中

### 3. 编写消息内容
1. 在"群发消息内容"文本框中输入要发送的消息
2. 支持多行文本和特殊字符
3. 建议控制消息长度以确保发送成功

### 4. 开始群发
1. 确认电话号码列表和消息内容无误
2. 点击"开始群发"按钮
3. 在确认对话框中点击"是"
4. 系统开始异步发送消息
5. 按钮显示发送进度
6. 发送完成后显示结果统计

## 安全考虑

### 1. 输入验证
- 严格的电话号码格式验证
- 防止SQL注入和XSS攻击
- 消息内容长度限制

### 2. 发送限制
- 发送频率控制（防止被封号）
- 批量发送数量限制
- 错误重试机制

### 3. 用户确认
- 发送前二次确认
- 清晰的操作提示
- 详细的结果反馈

## 扩展功能

### 未来可能的增强功能
1. **模板管理**: 预设消息模板
2. **定时发送**: 指定发送时间
3. **发送记录**: 历史发送记录查询
4. **联系人导入**: 从文件批量导入号码
5. **发送统计**: 详细的发送成功率统计
6. **黑名单管理**: 排除特定号码
7. **API集成**: 对接第三方短信/WhatsApp服务

## 注意事项

1. **合规使用**: 请确保群发消息符合当地法律法规
2. **频率控制**: 避免过于频繁发送导致账号被限制
3. **内容审核**: 确保发送内容合法合规
4. **用户同意**: 建议获得接收方同意后再发送
5. **测试先行**: 建议先小批量测试再大规模发送

## 故障排除

### 常见问题
1. **号码格式错误**: 检查电话号码格式是否正确
2. **发送失败**: 检查网络连接和API配置
3. **界面无响应**: 等待发送任务完成或重启应用
4. **按钮无效**: 确保已添加号码和消息内容

### 技术支持
如遇到技术问题，请检查：
1. 应用程序日志文件
2. 网络连接状态
3. 系统资源使用情况
4. 相关服务API状态
