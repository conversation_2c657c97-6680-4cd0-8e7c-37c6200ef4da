const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs');
const path = require('path');

// 获取从Python传递的环境变量
const USERNAME = process.env.USERNAME || 'default_user';
const STORE_NAME = process.env.STORE_NAME || 'bulk_messaging';
const SESSION_DIR = process.env.SESSION_DIR || 'default_session';
const STORE_DATA_DIR = process.env.STORE_DATA_DIR || path.join(__dirname, 'store_data', SESSION_DIR);
const LOGIN_STATUS_FILE = process.env.LOGIN_STATUS_FILE || path.join(STORE_DATA_DIR, 'login_status.json');

console.log(`启动WhatsApp群发登录服务`);
console.log(`用户: ${USERNAME}`);
console.log(`店铺: ${STORE_NAME}`);
console.log(`会话目录: ${SESSION_DIR}`);
console.log(`数据目录: ${STORE_DATA_DIR}`);
console.log(`状态文件: ${LOGIN_STATUS_FILE}`);

// 确保数据目录存在
if (!fs.existsSync(STORE_DATA_DIR)) {
  fs.mkdirSync(STORE_DATA_DIR, { recursive: true });
}

// 更新登录状态的函数
function updateLoginStatus(status, data = {}) {
  try {
    const statusData = {
      status: status,
      timestamp: Date.now(),
      username: USERNAME,
      store_name: STORE_NAME,
      ...data
    };
    
    fs.writeFileSync(LOGIN_STATUS_FILE, JSON.stringify(statusData, null, 2));
    console.log(`状态更新: ${status}`, data);
  } catch (error) {
    console.error('更新登录状态时出错:', error);
  }
}

// 初始化状态
updateLoginStatus('initializing');

// 创建WhatsApp客户端实例
const client = new Client({
  authStrategy: new LocalAuth({
    dataPath: path.join(STORE_DATA_DIR, 'session') // 使用独立的会话目录
  }),
  puppeteer: {
    headless: false, // 显示浏览器窗口，方便用户看到登录过程
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-web-security',
      '--disable-features=IsolateOrigins,site-per-process',
      '--ignore-certificate-errors',
      '--allow-running-insecure-content',
      '--window-size=1280,720',
      '--disable-gpu',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-default-browser-check',
      '--disable-notifications'
    ],
    defaultViewport: null,
    timeout: 120000, // 增加超时时间到120秒
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH
  }
});

// 当需要扫码登录时，将二维码展示在终端并更新状态
client.on('qr', (qr) => {
  console.log('请使用WhatsApp扫描以下二维码登录:');
  qrcode.generate(qr, { small: true });
  
  // 更新状态，包含QR码数据
  updateLoginStatus('qr_code', {
    qr_code: qr,
    message: '请使用WhatsApp扫描二维码登录'
  });
});

// 客户端准备加载中
client.on('loading_screen', (percent, message) => {
  console.log('加载中... ', percent, '%', message);
  updateLoginStatus('loading', {
    percent: percent,
    message: message
  });
});

// 客户端已认证（不需要扫码）
client.on('authenticated', () => {
  console.log('认证成功!');
  updateLoginStatus('authenticated', {
    message: 'WhatsApp认证成功'
  });
});

// 认证失败
client.on('auth_failure', (msg) => {
  console.error('认证失败:', msg);
  updateLoginStatus('error', {
    message: `认证失败: ${msg}`
  });
});

// 客户端已准备好
client.on('ready', () => {
  console.log('客户端已准备就绪，群发功能可用!');
  updateLoginStatus('ready', {
    message: 'WhatsApp客户端已就绪，可以开始群发消息'
  });
  
  // 启动群发消息监听
  startBulkMessageListener();
});

// 监听断开连接事件
client.on('disconnected', (reason) => {
  console.log('客户端已断开连接:', reason);
  updateLoginStatus('disconnected', {
    reason: reason,
    message: `连接已断开: ${reason}`
  });
  
  // 可以选择是否自动重连
  // console.log('正在尝试重新连接...');
  // client.initialize();
});

// 群发消息监听器
function startBulkMessageListener() {
  const BULK_SEND_FILE = path.join(STORE_DATA_DIR, 'bulk_send_requests.json');
  const BULK_STATUS_FILE = path.join(STORE_DATA_DIR, 'bulk_send_status.json');
  
  console.log('启动群发消息监听器');
  console.log(`监听文件: ${BULK_SEND_FILE}`);
  console.log(`状态文件: ${BULK_STATUS_FILE}`);
  
  // 初始化状态文件
  if (!fs.existsSync(BULK_STATUS_FILE)) {
    fs.writeFileSync(BULK_STATUS_FILE, JSON.stringify({
      status: 'idle',
      timestamp: Date.now()
    }));
  }
  
  // 定时检查群发请求
  setInterval(() => {
    checkBulkSendRequests(BULK_SEND_FILE, BULK_STATUS_FILE);
  }, 1000);
}

// 检查并处理群发请求
function checkBulkSendRequests(requestFile, statusFile) {
  try {
    if (!fs.existsSync(requestFile)) {
      return;
    }
    
    const fileContent = fs.readFileSync(requestFile, 'utf8');
    if (!fileContent.trim()) {
      return;
    }
    
    const request = JSON.parse(fileContent);
    if (!request.action || request.action !== 'send_bulk') {
      return;
    }
    
    // 清空请求文件
    fs.writeFileSync(requestFile, '');
    
    // 开始处理群发请求
    processBulkSendRequest(request, statusFile);
    
  } catch (error) {
    console.error('检查群发请求时出错:', error);
    updateBulkStatus(statusFile, 'error', {
      message: `检查请求失败: ${error.message}`
    });
  }
}

// 验证电话号码是否为正确的WhatsApp格式
function validateWhatsAppFormat(phoneNumber) {
  // 检查是否以@c.us结尾
  if (!phoneNumber.endsWith('@c.us')) {
    console.warn(`电话号码格式可能不正确: ${phoneNumber}`);
    // 如果没有@c.us后缀，尝试添加
    return phoneNumber + '@c.us';
  }
  return phoneNumber;
}

// 处理群发请求
async function processBulkSendRequest(request, statusFile) {
  const { phone_numbers, message, delay = 3000 } = request;

  console.log(`开始群发消息到 ${phone_numbers.length} 个号码`);

  updateBulkStatus(statusFile, 'sending', {
    total_count: phone_numbers.length,
    sent_count: 0,
    current_phone: '',
    message: '开始群发消息'
  });

  let sentCount = 0;
  const errors = [];

  for (let i = 0; i < phone_numbers.length; i++) {
    const phoneNumber = phone_numbers[i];
    const validatedPhoneNumber = validateWhatsAppFormat(phoneNumber);

    try {
      console.log(`正在发送消息到: ${phoneNumber} (${i + 1}/${phone_numbers.length})`);

      updateBulkStatus(statusFile, 'sending', {
        total_count: phone_numbers.length,
        sent_count: sentCount,
        current_phone: phoneNumber,
        progress: Math.round((i / phone_numbers.length) * 100),
        message: `正在发送到 ${phoneNumber}`
      });

      // 发送消息（使用验证后的格式）
      await client.sendMessage(validatedPhoneNumber, message);
      sentCount++;

      console.log(`消息发送成功: ${phoneNumber}`);

      // 更新进度
      updateBulkStatus(statusFile, 'sending', {
        total_count: phone_numbers.length,
        sent_count: sentCount,
        current_phone: phoneNumber,
        progress: Math.round(((i + 1) / phone_numbers.length) * 100),
        message: `已发送到 ${phoneNumber}`
      });

      // 延迟，避免发送过快
      if (i < phone_numbers.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      console.error(`发送消息到 ${phoneNumber} 失败:`, error);
      errors.push({
        phone: phoneNumber,
        error: error.message
      });
    }
  }
  
  // 发送完成
  console.log(`群发完成! 成功: ${sentCount}/${phone_numbers.length}`);
  
  updateBulkStatus(statusFile, 'completed', {
    total_count: phone_numbers.length,
    sent_count: sentCount,
    failed_count: phone_numbers.length - sentCount,
    progress: 100,
    errors: errors,
    message: `群发完成! 成功发送 ${sentCount}/${phone_numbers.length} 条消息`
  });
}

// 更新群发状态
function updateBulkStatus(statusFile, status, data = {}) {
  try {
    const statusData = {
      status: status,
      timestamp: Date.now(),
      username: USERNAME,
      store_name: STORE_NAME,
      ...data
    };
    
    fs.writeFileSync(statusFile, JSON.stringify(statusData, null, 2));
  } catch (error) {
    console.error('更新群发状态时出错:', error);
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('收到退出信号，正在清理...');
  updateLoginStatus('disconnected', {
    message: '用户主动退出'
  });
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('收到终止信号，正在清理...');
  updateLoginStatus('disconnected', {
    message: '进程被终止'
  });
  process.exit(0);
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);

  // 忽略EPIPE错误，这通常是由于父进程关闭导致的
  if (error.code === 'EPIPE' || error.errno === -32) {
    console.log('检测到EPIPE错误，正常退出');
    process.exit(0);
  }

  updateLoginStatus('error', {
    message: `未捕获的异常: ${error.message}`
  });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  updateLoginStatus('error', {
    message: `未处理的Promise拒绝: ${reason}`
  });
});

// 处理EPIPE错误
process.stdout.on('error', (error) => {
  if (error.code === 'EPIPE') {
    console.error('stdout EPIPE错误，父进程可能已关闭');
    process.exit(0);
  }
});

process.stderr.on('error', (error) => {
  if (error.code === 'EPIPE') {
    console.error('stderr EPIPE错误，父进程可能已关闭');
    process.exit(0);
  }
});

// 初始化客户端
console.log('正在初始化WhatsApp客户端...');
client.initialize();

console.log(`WhatsApp群发登录服务已启动 - 用户: ${USERNAME}, 店铺: ${STORE_NAME}`);
