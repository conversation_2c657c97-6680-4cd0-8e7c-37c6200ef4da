# ///////////////////////////////////////////////////////////////
#
# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

# IMPORT PACKAGES AND MODULES
# ///////////////////////////////////////////////////////////////
from gui.uis.windows.main_window.functions_main_window import *
import sys
import os

# IMPORT QT CORE
# ///////////////////////////////////////////////////////////////
from qt_core import *

# IMPORT SETTINGS
# ///////////////////////////////////////////////////////////////
from gui.core.json_settings import Settings

# 导入知识库管理功能
from gui.core.knowledge_base_functions import KnowledgeBaseManager
# 导入AI客服控制器
from gui.core.ai_service_controller import AIServiceController
# 导入意向分析控制器
from gui.core.intent_analysis_controller import IntentAnalysisController
# 导入WhatsApp批量消息控制器
from gui.core.whatsapp_bulk_controller import WhatsAppBulkController

# IMPORT PY ONE DARK WINDOWS
# ///////////////////////////////////////////////////////////////
# MAIN WINDOW
from gui.uis.windows.main_window import *

# IMPORT PY ONE DARK WIDGETS
# ///////////////////////////////////////////////////////////////
from gui.widgets import *

# ADJUST QT FONT DPI FOR HIGHT SCALE AN 4K MONITOR
# ///////////////////////////////////////////////////////////////
os.environ["QT_FONT_DPI"] = "96"
# IF IS 4K MONITOR ENABLE 'os.environ["QT_SCALE_FACTOR"] = "2"'

# MAIN WINDOW
# ///////////////////////////////////////////////////////////////
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # SETUP MAIN WINDOw
        # Load widgets from "gui\uis\main_window\ui_main.py"
        # ///////////////////////////////////////////////////////////////
        self.ui = UI_MainWindow()
        self.ui.setup_ui(self)

        # LOAD SETTINGS
        # ///////////////////////////////////////////////////////////////
        settings = Settings()
        self.settings = settings.items

        # SETUP MAIN WINDOW
        # ///////////////////////////////////////////////////////////////
        self.hide_grips = True # Show/Hide resize grips
        SetupMainWindow.setup_gui(self)

        # 初始化知识库管理器
        self.kb_manager = KnowledgeBaseManager(self)
        
        # 初始化控制器（带异常处理）
        try:
            # 初始化AI客服控制器
            print("正在初始化AI客服控制器...")
            self.ai_service_controller = AIServiceController(self)
            print("✓ AI客服控制器初始化成功")
        except Exception as e:
            print(f"✗ AI客服控制器初始化失败: {e}")
            import traceback
            traceback.print_exc()

        try:
            # 初始化意向分析控制器
            print("正在初始化意向分析控制器...")
            self.intent_analysis_controller = IntentAnalysisController(self)
            print("✓ 意向分析控制器初始化成功")
        except Exception as e:
            print(f"✗ 意向分析控制器初始化失败: {e}")
            import traceback
            traceback.print_exc()

        try:
            # 初始化WhatsApp批量消息控制器
            print("正在初始化WhatsApp批量消息控制器...")
            self.whatsapp_bulk_controller = WhatsAppBulkController(self)
            print("✓ WhatsApp批量消息控制器初始化成功")
        except Exception as e:
            print(f"✗ WhatsApp批量消息控制器初始化失败: {e}")
            import traceback
            traceback.print_exc()
            # 这个错误比较严重，需要显示给用户
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "初始化错误",
                f"WhatsApp批量消息功能初始化失败:\n{str(e)}\n\n请检查日志文件获取详细信息。"
            )
        
        # 连接知识库管理按钮信号
        self.ui.load_pages.core_upload_btn.clicked.connect(lambda: self.kb_manager.upload_files_dialog("core"))
        self.ui.load_pages.core_generate_btn.clicked.connect(lambda: self.kb_manager.generate_knowledge_base("core"))
        # self.ui.load_pages.core_graph_btn.clicked.connect(lambda: self.kb_manager.generate_knowledge_graph("core"))
        self.ui.load_pages.help_upload_btn.clicked.connect(lambda: self.kb_manager.upload_files_dialog("help"))
        self.ui.load_pages.help_generate_btn.clicked.connect(lambda: self.kb_manager.generate_knowledge_base("help"))
        # self.ui.load_pages.help_graph_btn.clicked.connect(lambda: self.kb_manager.generate_knowledge_graph("help"))
        
        # 初始化时预先加载文件列表数据
        self.kb_manager.refresh_file_list("core")
        self.kb_manager.refresh_file_list("help")
        
        # 手动添加AI客服按钮的点击处理
        for button in self.ui.left_menu.findChildren(QPushButton):
            if button.objectName() == "btn_ai_customer_service":
                button.clicked.connect(self.show_ai_customer_service)
                print("找到并连接AI客服按钮")
            elif button.objectName() == "btn_bulk_customer_acquisition":
                button.clicked.connect(self.show_bulk_customer_acquisition)
                print("找到并连接群发获客按钮")

        # SHOW MAIN WINDOW
        # ///////////////////////////////////////////////////////////////
        self.show()

    # LEFT MENU BTN IS CLICKED
    # Run function when btn is clicked
    # Check funtion by object name / btn_id
    # ///////////////////////////////////////////////////////////////
    def btn_clicked(self):
        # GET BT CLICKED
        btn = SetupMainWindow.setup_btns(self)

        # Remove Selection If Clicked By "btn_close_left_column"
        if btn.objectName() == "btn_close_left_column":
            self.ui.left_menu.deselect_all_tab()

        # Get Title Bar Btn And Reset Active         
        top_settings = MainFunctions.get_title_bar_btn(self, "btn_top_settings")
        top_settings.set_active(False)

        # 确保点击左侧菜单按钮时展开菜单
        if btn.objectName() in ["btn_home", "btn_knowledge_base", "btn_ai_customer_service", "btn_chat_records", "btn_chat_test", "btn_intent_analysis", "btn_bulk_customer_acquisition"]:
            if self.ui.left_menu.width() == self.settings["lef_menu_size"]["minimum"]:
                self.ui.left_menu.toggle_animation()
        
        # LEFT MENU
        # ///////////////////////////////////////////////////////////////
        
        # HOME BTN
        if btn.objectName() == "btn_home":
            # Select Menu
            self.ui.left_menu.select_only_one(btn.objectName())

            # Load Page 1
            MainFunctions.set_page(self, self.ui.load_pages.page_1)

        # LOAD KNOWLEDGE BASE PAGE
        if btn.objectName() == "btn_knowledge_base":
            # Select Menu
            self.ui.left_menu.select_only_one(btn.objectName())

            # Load Page 3 
            MainFunctions.set_page(self, self.ui.load_pages.page_3)
            
            # 刷新文件列表
            self.kb_manager.refresh_file_list("core")
            self.kb_manager.refresh_file_list("help")
            
        # LOAD INTENT ANALYSIS PAGE
        if btn.objectName() == "btn_intent_analysis":
            # Select Menu
            self.ui.left_menu.select_only_one(btn.objectName())

            # Load Page 8 (意向分析页面)
            MainFunctions.set_page(self, self.ui.load_pages.page_8)

            # 加载店铺列表和意向分析数据
            self.intent_analysis_controller.load_stores()
            self.intent_analysis_controller.load_intent_data()

        # LOAD CHAT RECORDS PAGE
        if btn.objectName() == "btn_chat_records":
            # Select Menu
            self.ui.left_menu.select_only_one(btn.objectName())

            # Load Page 6 (聊天记录页面)
            MainFunctions.set_page(self, self.ui.load_pages.page_6)

        # LOAD CHAT TEST PAGE
        if btn.objectName() == "btn_chat_test":
            # Select Menu
            self.ui.left_menu.select_only_one(btn.objectName())

            # Load Page 7 (聊天测试页面)
            MainFunctions.set_page(self, self.ui.load_pages.page_7)

        # LOAD BULK CUSTOMER ACQUISITION PAGE
        if btn.objectName() == "btn_bulk_customer_acquisition":
            # Select Menu
            self.ui.left_menu.select_only_one(btn.objectName())

            # Load Page 9 (群发获客页面)
            MainFunctions.set_page(self, self.ui.load_pages.page_9)

            # 设置控制器UI组件
            if hasattr(self, 'whatsapp_bulk_controller'):
                try:
                    self.whatsapp_bulk_controller.setup_ui_components(self.ui.load_pages.page_9)
                except Exception as e:
                    print(f"设置WhatsApp批量消息UI组件失败: {e}")
        
        # TITLE BAR MENU
        # ///////////////////////////////////////////////////////////////
        
        # SETTINGS TITLE BAR
        if btn.objectName() == "btn_top_settings":
            # Toogle Active
            if not MainFunctions.right_column_is_visible(self):
                btn.set_active(True)

                # Show / Hide
                MainFunctions.toggle_right_column(self)
            else:
                btn.set_active(False)

                # Show / Hide
                MainFunctions.toggle_right_column(self)       

        # DEBUG
        print(f"Button {btn.objectName()}, clicked!")

    # 单独处理AI客服按钮点击
    def show_ai_customer_service(self):
        """显示AI客服页面的特定方法"""
        print("AI客服按钮被点击")
        # 选择按钮并高亮
        self.ui.left_menu.select_only_one("btn_ai_customer_service")
        # 显示对应页面
        MainFunctions.set_page(self, self.ui.load_pages.page_5)

    # 单独处理群发获客按钮点击
    def show_bulk_customer_acquisition(self):
        """显示群发获客页面的特定方法"""
        print("群发获客按钮被点击")
        # 选择按钮并高亮
        self.ui.left_menu.select_only_one("btn_bulk_customer_acquisition")
        # 显示对应页面
        MainFunctions.set_page(self, self.ui.load_pages.page_9)

        # 设置控制器UI组件
        if hasattr(self, 'whatsapp_bulk_controller'):
            try:
                self.whatsapp_bulk_controller.setup_ui_components(self.ui.load_pages.page_9)
            except Exception as e:
                print(f"设置WhatsApp批量消息UI组件失败: {e}")

    # LEFT MENU BTN IS RELEASED
    # Run function when btn is released
    # Check funtion by object name / btn_id
    # ///////////////////////////////////////////////////////////////
    def btn_released(self):
        # GET BT CLICKED
        btn = SetupMainWindow.setup_btns(self)

        # DEBUG
        print(f"Button {btn.objectName()}, released!")

    # RESIZE EVENT
    # ///////////////////////////////////////////////////////////////
    def resizeEvent(self, event):
        SetupMainWindow.resize_grips(self)

    # MOUSE CLICK EVENTS
    # ///////////////////////////////////////////////////////////////
    def mousePressEvent(self, event):
        # SET DRAG POS WINDOW
        self.dragPos = event.globalPos()


# SETTINGS WHEN TO START
# Set the initial class and also additional parameters of the "QApplication" class
# ///////////////////////////////////////////////////////////////
if __name__ == "__main__":
    # APPLICATION
    # ///////////////////////////////////////////////////////////////
    app = QApplication(sys.argv)
    # 使用绝对路径设置程序图标
    icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "icon.ico")
    app.setWindowIcon(QIcon(icon_path))
    window = MainWindow()

    # EXEC APP
    # ///////////////////////////////////////////////////////////////
    sys.exit(app.exec())