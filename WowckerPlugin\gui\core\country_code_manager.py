"""
国家代码管理器
负责管理国际电话号码的国家代码和验证规则
"""

import re
from typing import Dict, List, Tuple, Optional

class CountryCodeManager:
    """国家代码管理器"""
    
    def __init__(self):
        """初始化国家代码数据"""
        self.countries = {
            # 常用国家（排在前面）
            "86": {
                "name_cn": "中国",
                "name_en": "China",
                "code": "+86",
                "flag": "🇨🇳",
                "pattern": r"^1[3-9]\d{9}$",  # 11位手机号
                "format_example": "138 1234 5678",
                "common": True
            },
            "1": {
                "name_cn": "美国",
                "name_en": "United States",
                "code": "+1",
                "flag": "🇺🇸",
                "pattern": r"^\d{10}$",  # 10位号码
                "format_example": "555 123 4567",
                "common": True
            },
            "44": {
                "name_cn": "英国",
                "name_en": "United Kingdom",
                "code": "+44",
                "flag": "🇬🇧",
                "pattern": r"^[1-9]\d{8,10}$",  # 9-11位号码
                "format_example": "7700 900123",
                "common": True
            },
            "81": {
                "name_cn": "日本",
                "name_en": "Japan",
                "code": "+81",
                "flag": "🇯🇵",
                "pattern": r"^[1-9]\d{8,10}$",  # 9-11位号码
                "format_example": "90 1234 5678",
                "common": True
            },
            "82": {
                "name_cn": "韩国",
                "name_en": "South Korea",
                "code": "+82",
                "flag": "🇰🇷",
                "pattern": r"^1[0-9]\d{7,8}$",  # 9-10位号码
                "format_example": "10 1234 5678",
                "common": True
            },
            
            # 其他国家
            "33": {
                "name_cn": "法国",
                "name_en": "France",
                "code": "+33",
                "flag": "🇫🇷",
                "pattern": r"^[1-9]\d{8}$",
                "format_example": "6 12 34 56 78",
                "common": False
            },
            "49": {
                "name_cn": "德国",
                "name_en": "Germany",
                "code": "+49",
                "flag": "🇩🇪",
                "pattern": r"^1[5-7]\d{8,9}$",
                "format_example": "151 12345678",
                "common": False
            },
            "39": {
                "name_cn": "意大利",
                "name_en": "Italy",
                "code": "+39",
                "flag": "🇮🇹",
                "pattern": r"^3\d{8,9}$",
                "format_example": "312 345 6789",
                "common": False
            },
            "34": {
                "name_cn": "西班牙",
                "name_en": "Spain",
                "code": "+34",
                "flag": "🇪🇸",
                "pattern": r"^[6-9]\d{8}$",
                "format_example": "612 34 56 78",
                "common": False
            },
            "7": {
                "name_cn": "俄罗斯",
                "name_en": "Russia",
                "code": "+7",
                "flag": "🇷🇺",
                "pattern": r"^9\d{9}$",
                "format_example": "912 345 6789",
                "common": False
            },
            "91": {
                "name_cn": "印度",
                "name_en": "India",
                "code": "+91",
                "flag": "🇮🇳",
                "pattern": r"^[6-9]\d{9}$",
                "format_example": "98765 43210",
                "common": False
            },
            "55": {
                "name_cn": "巴西",
                "name_en": "Brazil",
                "code": "+55",
                "flag": "🇧🇷",
                "pattern": r"^[1-9]\d{8,10}$",
                "format_example": "11 91234 5678",
                "common": False
            },
            "61": {
                "name_cn": "澳大利亚",
                "name_en": "Australia",
                "code": "+61",
                "flag": "🇦🇺",
                "pattern": r"^4\d{8}$",
                "format_example": "***********",
                "common": False
            },
            "65": {
                "name_cn": "新加坡",
                "name_en": "Singapore",
                "code": "+65",
                "flag": "🇸🇬",
                "pattern": r"^[89]\d{7}$",
                "format_example": "8123 4567",
                "common": False
            },
            "60": {
                "name_cn": "马来西亚",
                "name_en": "Malaysia",
                "code": "+60",
                "flag": "🇲🇾",
                "pattern": r"^1[0-9]\d{7,8}$",
                "format_example": "12 345 6789",
                "common": False
            },
            "66": {
                "name_cn": "泰国",
                "name_en": "Thailand",
                "code": "+66",
                "flag": "🇹🇭",
                "pattern": r"^[689]\d{8}$",
                "format_example": "81 234 5678",
                "common": False
            },
            "84": {
                "name_cn": "越南",
                "name_en": "Vietnam",
                "code": "+84",
                "flag": "🇻🇳",
                "pattern": r"^[3-9]\d{8}$",
                "format_example": "91 234 5678",
                "common": False
            },
            "62": {
                "name_cn": "印度尼西亚",
                "name_en": "Indonesia",
                "code": "+62",
                "flag": "🇮🇩",
                "pattern": r"^8\d{8,11}$",
                "format_example": "812 3456 789",
                "common": False
            },
            "63": {
                "name_cn": "菲律宾",
                "name_en": "Philippines",
                "code": "+63",
                "flag": "🇵🇭",
                "pattern": r"^9\d{9}$",
                "format_example": "917 123 4567",
                "common": False
            }
        }
    
    def get_common_countries(self) -> List[Tuple[str, Dict]]:
        """获取常用国家列表"""
        common = [(code, info) for code, info in self.countries.items() if info.get("common", False)]
        # 按代码排序，中国排第一
        common.sort(key=lambda x: (x[0] != "86", x[0]))
        return common
    
    def get_all_countries(self) -> List[Tuple[str, Dict]]:
        """获取所有国家列表"""
        all_countries = list(self.countries.items())
        # 常用国家排前面，其他按代码排序
        common = [item for item in all_countries if item[1].get("common", False)]
        others = [item for item in all_countries if not item[1].get("common", False)]
        
        common.sort(key=lambda x: (x[0] != "86", x[0]))  # 中国排第一
        others.sort(key=lambda x: x[0])
        
        return common + others
    
    def get_country_info(self, country_code: str) -> Optional[Dict]:
        """获取指定国家的信息"""
        return self.countries.get(country_code)
    
    def validate_phone_number(self, country_code: str, local_number: str) -> Tuple[bool, str]:
        """验证电话号码格式"""
        country_info = self.get_country_info(country_code)
        if not country_info:
            return False, f"不支持的国家代码: +{country_code}"
        
        # 清理本地号码（移除空格、短横线等）
        cleaned_number = re.sub(r'[^\d]', '', local_number.strip())
        
        if not cleaned_number:
            return False, "电话号码不能为空"
        
        # 验证格式
        pattern = country_info["pattern"]
        if not re.match(pattern, cleaned_number):
            example = country_info["format_example"]
            return False, f"号码格式不正确，{country_info['name_cn']}号码格式示例: {example}"
        
        return True, cleaned_number
    
    def format_for_whatsapp(self, country_code: str, local_number: str) -> str:
        """格式化为WhatsApp格式"""
        return f"{country_code}{local_number}@c.us"
    
    def format_for_display(self, country_code: str, local_number: str) -> str:
        """格式化为显示格式"""
        country_info = self.get_country_info(country_code)
        if not country_info:
            return f"+{country_code} {local_number}"
        
        # 根据国家特点格式化显示
        if country_code == "86":  # 中国
            if len(local_number) == 11:
                return f"+86 {local_number[:3]} {local_number[3:7]} {local_number[7:]}"
        elif country_code == "1":  # 美国
            if len(local_number) == 10:
                return f"+1 ({local_number[:3]}) {local_number[3:6]}-{local_number[6:]}"
        elif country_code == "44":  # 英国
            if len(local_number) >= 10:
                return f"+44 {local_number[:4]} {local_number[4:7]} {local_number[7:]}"
        
        # 默认格式
        return f"+{country_code} {local_number}"
    
    def parse_international_number(self, full_number: str) -> Optional[Tuple[str, str]]:
        """解析完整的国际号码，返回(国家代码, 本地号码)"""
        # 清理号码
        cleaned = re.sub(r'[^\d+]', '', full_number.strip())
        
        if cleaned.startswith('+'):
            cleaned = cleaned[1:]
        
        # 尝试匹配已知的国家代码
        for code in sorted(self.countries.keys(), key=len, reverse=True):
            if cleaned.startswith(code):
                local_number = cleaned[len(code):]
                if local_number:
                    return code, local_number
        
        return None
    
    def search_countries(self, query: str) -> List[Tuple[str, Dict]]:
        """搜索国家（支持中文名、英文名、代码）"""
        query = query.lower().strip()
        if not query:
            return self.get_all_countries()
        
        results = []
        for code, info in self.countries.items():
            # 搜索代码、中文名、英文名
            if (query in code or 
                query in info["name_cn"].lower() or 
                query in info["name_en"].lower() or
                query in info["code"].lower()):
                results.append((code, info))
        
        # 排序：常用国家优先，然后按匹配度
        results.sort(key=lambda x: (not x[1].get("common", False), x[0]))
        return results
