#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断群发获客页面按钮连接问题的脚本
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "PyOneDark_Qt_Widgets_Modern_GUI-master"))

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_ui_component_existence():
    """检查UI组件是否正确创建"""
    try:
        from gui.uis.pages.ui_main_pages import Ui_MainPages
        
        # 创建模拟的主页面对象
        class MockMainPages:
            def __init__(self):
                pass
        
        mock_pages = MockMainPages()
        ui = Ui_MainPages()
        ui.setupUi(mock_pages)
        
        # 检查关键组件是否存在
        components_to_check = [
            'add_phone_btn',
            'whatsapp_login_btn', 
            'whatsapp_logout_btn',
            'bulk_send_btn',
            'login_status_indicator',
            'login_status_text',
            'login_info_text',
            'send_status_label',
            'message_input_field',
            'phone_numbers_table'
        ]
        
        missing_components = []
        existing_components = []
        
        for component_name in components_to_check:
            if hasattr(ui, component_name):
                component = getattr(ui, component_name)
                existing_components.append(component_name)
                logger.info(f"✓ 组件 {component_name} 存在: {type(component).__name__}")
            else:
                missing_components.append(component_name)
                logger.error(f"✗ 组件 {component_name} 不存在")
        
        return existing_components, missing_components
        
    except Exception as e:
        logger.error(f"检查UI组件时出错: {e}")
        return [], []

def check_controller_initialization():
    """检查控制器初始化过程"""
    try:
        from gui.core.bulk_customer_acquisition_controller import BulkCustomerAcquisitionController
        
        # 创建模拟的主窗口
        class MockMainWindow:
            def __init__(self):
                self.current_username = 'test_user'
                # 创建模拟的UI结构
                self.ui = MockUI()
        
        class MockUI:
            def __init__(self):
                self.load_pages = MockLoadPages()
        
        class MockLoadPages:
            def __init__(self):
                # 创建模拟的UI组件
                from PySide6.QtWidgets import QWidget, QPushButton, QLabel, QTextEdit, QTableWidget
                
                self.message_input_field = QTextEdit()
                self.phone_numbers_table = QTableWidget()
                self.bulk_send_btn = QPushButton("群发消息")
                self.add_phone_btn = QPushButton("添加号码")
                self.login_status_indicator = QLabel("●")
                self.login_status_text = QLabel("未登录")
                self.login_info_text = QLabel("请先登录WhatsApp才能使用群发功能")
                self.whatsapp_login_btn = QPushButton("登录WhatsApp")
                self.whatsapp_logout_btn = QPushButton("退出登录")
                self.send_status_label = QLabel("请先登录WhatsApp并输入消息")
        
        mock_window = MockMainWindow()
        
        # 尝试初始化控制器
        controller = BulkCustomerAcquisitionController(mock_window)
        logger.info("✓ 控制器初始化成功")
        
        # 检查信号连接
        logger.info("检查信号连接...")
        
        # 检查按钮是否有clicked信号
        if hasattr(controller.add_phone_btn, 'clicked'):
            logger.info("✓ add_phone_btn 有 clicked 信号")
        else:
            logger.error("✗ add_phone_btn 没有 clicked 信号")
            
        if hasattr(controller.whatsapp_login_btn, 'clicked'):
            logger.info("✓ whatsapp_login_btn 有 clicked 信号")
        else:
            logger.error("✗ whatsapp_login_btn 没有 clicked 信号")
            
        if hasattr(controller.bulk_send_btn, 'clicked'):
            logger.info("✓ bulk_send_btn 有 clicked 信号")
        else:
            logger.error("✗ bulk_send_btn 没有 clicked 信号")
        
        # 检查槽函数是否存在
        slot_functions = [
            'add_phone_number_dialog',
            'start_whatsapp_login',
            'logout_whatsapp',
            'start_bulk_send'
        ]
        
        for slot_name in slot_functions:
            if hasattr(controller, slot_name):
                logger.info(f"✓ 槽函数 {slot_name} 存在")
            else:
                logger.error(f"✗ 槽函数 {slot_name} 不存在")
        
        return True
        
    except ImportError as e:
        logger.error(f"导入错误: {e}")
        return False
    except Exception as e:
        logger.error(f"控制器初始化错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主诊断函数"""
    logger.info("开始诊断群发获客页面按钮连接问题...")
    logger.info("=" * 60)
    
    # 检查UI组件
    logger.info("1. 检查UI组件是否正确创建...")
    try:
        existing, missing = check_ui_component_existence()
        if missing:
            logger.error(f"缺失的UI组件: {missing}")
        else:
            logger.info("所有UI组件都存在")
    except Exception as e:
        logger.error(f"UI组件检查失败: {e}")
    
    logger.info("-" * 40)
    
    # 检查控制器初始化
    logger.info("2. 检查控制器初始化...")
    try:
        success = check_controller_initialization()
        if success:
            logger.info("控制器初始化检查完成")
        else:
            logger.error("控制器初始化检查失败")
    except Exception as e:
        logger.error(f"控制器检查失败: {e}")
    
    logger.info("=" * 60)
    logger.info("诊断完成")

if __name__ == "__main__":
    main()
