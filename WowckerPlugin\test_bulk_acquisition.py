"""
测试群发获客功能
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master'))

def test_bulk_acquisition_controller():
    """测试群发获客控制器"""
    try:
        # 模拟控制器类，因为实际导入需要完整的GUI环境
        import re

        def validate_phone_number(phone):
            """验证电话号码格式"""
            # 移除所有空格和特殊字符，只保留数字和+号
            cleaned_phone = re.sub(r'[^\d+]', '', phone.strip())

            # 检查是否为空
            if not cleaned_phone:
                return False, "电话号码不能为空"

            # 检查格式（支持国际格式和国内格式）
            if cleaned_phone.startswith('+'):
                # 国际格式
                if len(cleaned_phone) < 10 or len(cleaned_phone) > 15:
                    return False, "国际电话号码长度应在10-15位之间"
            else:
                # 国内格式
                if len(cleaned_phone) != 11:
                    return False, "国内电话号码应为11位"
                if not cleaned_phone.startswith(('13', '14', '15', '16', '17', '18', '19')):
                    return False, "请输入有效的手机号码"

            return True, cleaned_phone

        print("✓ 群发获客控制器逻辑测试开始")
        
        # 测试有效的电话号码
        test_phones = [
            "+8613812345678",
            "13812345678", 
            "+1234567890",
            "15912345678"
        ]
        
        for phone in test_phones:
            is_valid, result = validate_phone_number(phone)
            if is_valid:
                print(f"✓ 电话号码 {phone} 验证通过: {result}")
            else:
                print(f"✗ 电话号码 {phone} 验证失败: {result}")

        # 测试无效的电话号码
        invalid_phones = [
            "123",
            "abc123",
            "",
            "12345678901234567890"
        ]

        for phone in invalid_phones:
            is_valid, result = validate_phone_number(phone)
            if not is_valid:
                print(f"✓ 无效电话号码 {phone} 正确被拒绝: {result}")
            else:
                print(f"✗ 无效电话号码 {phone} 错误通过验证")
                
        print("群发获客控制器测试完成")
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_ui_components():
    """测试UI组件是否正确创建"""
    try:
        print("✓ 检查UI文件是否存在...")

        # 检查关键文件是否存在
        import os
        ui_file = os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master', 'gui', 'uis', 'pages', 'ui_main_pages.py')
        controller_file = os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master', 'gui', 'core', 'bulk_customer_acquisition_controller.py')

        if os.path.exists(ui_file):
            print("✓ UI主页面文件存在")

            # 检查文件内容是否包含群发获客相关代码
            with open(ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'page_9' in content and 'bulk_acquisition' in content:
                    print("✓ UI文件包含群发获客页面代码")
                else:
                    print("✗ UI文件缺少群发获客页面代码")
        else:
            print("✗ UI主页面文件不存在")

        if os.path.exists(controller_file):
            print("✓ 群发获客控制器文件存在")
        else:
            print("✗ 群发获客控制器文件不存在")

        print("UI组件测试完成")

    except Exception as e:
        print(f"✗ UI测试失败: {e}")

def test_menu_configuration():
    """测试菜单配置"""
    try:
        print("✓ 检查菜单配置文件...")

        import os
        setup_file = os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master', 'gui', 'uis', 'windows', 'main_window', 'setup_main_window.py')

        if os.path.exists(setup_file):
            print("✓ 主窗口设置文件存在")

            # 检查文件内容是否包含群发获客按钮配置
            with open(setup_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'btn_bulk_customer_acquisition' in content:
                    print("✓ 菜单配置包含群发获客按钮")
                else:
                    print("✗ 菜单配置缺少群发获客按钮")
        else:
            print("✗ 主窗口设置文件不存在")

        print("菜单配置测试完成")

    except Exception as e:
        print(f"✗ 菜单配置测试失败: {e}")

if __name__ == "__main__":
    print("开始测试群发获客功能...")
    print("=" * 50)
    
    test_bulk_acquisition_controller()
    print("-" * 30)
    
    test_ui_components()
    print("-" * 30)
    
    test_menu_configuration()
    print("-" * 30)
    
    print("测试完成!")
