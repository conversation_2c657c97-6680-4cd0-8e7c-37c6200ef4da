2025-06-21 15:21:36,710 - __main__ - INFO - 开始测试按钮修复...
2025-06-21 15:21:36,711 - __main__ - INFO - ============================================================
2025-06-21 15:21:36,712 - __main__ - ERROR - ✗ 无法导入PySide6: No module named 'PySide6'
2025-06-21 15:21:36,712 - __main__ - INFO - ✓ 创建了模拟UI组件
2025-06-21 15:21:36,725 - __main__ - ERROR - 测试UI组件访问时出错: No module named 'PySide6'
Traceback (most recent call last):
  File "test_button_fix.py", line 107, in test_ui_component_access
    from gui.core.bulk_customer_acquisition_controller import BulkCustomerAcquisitionController
  File "C:\Users\<USER>\Desktop\all_plugin\WowckerPlugin\PyOneDark_Qt_Widgets_Modern_GUI-master\gui\core\bulk_customer_acquisition_controller.py", line 11, in <module>
    from PySide6.QtWidgets import QMessageBox, QTableWidgetItem, QPushButton, QLabel, QVBoxLayout, QHBoxLayout, QFrame
ModuleNotFoundError: No module named 'PySide6'
2025-06-21 15:21:36,726 - __main__ - ERROR - ✗ 测试失败，可能仍有问题
2025-06-21 15:21:36,726 - __main__ - INFO - ============================================================
2025-06-21 15:21:36,726 - __main__ - INFO - 测试结束
