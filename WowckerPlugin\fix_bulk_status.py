"""
快速修复群发获客功能状态的脚本
"""

import os
import json
import sys
import subprocess
from pathlib import Path

def kill_node_processes():
    """终止所有Node.js进程"""
    print("=== 终止Node.js进程 ===")
    
    try:
        if sys.platform == 'win32':
            # Windows系统
            result = subprocess.run(['taskkill', '/F', '/IM', 'node.exe'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 已终止所有Node.js进程")
            else:
                print("⚠️ 没有找到Node.js进程或终止失败")
        else:
            # Linux/Mac系统
            result = subprocess.run(['pkill', '-f', 'node'], 
                                  capture_output=True, text=True)
            print("✅ 已尝试终止Node.js进程")
            
    except Exception as e:
        print(f"❌ 终止进程失败: {e}")

def reset_status_files():
    """重置所有状态文件"""
    print("\n=== 重置状态文件 ===")
    
    base_dir = Path(__file__).parent / "chat" / "whatsapp" / "store_data"
    
    if not base_dir.exists():
        print("❌ store_data目录不存在")
        return
    
    # 查找所有群发会话目录
    bulk_session_dirs = [d for d in base_dir.iterdir() if d.is_dir() and d.name.startswith("bulk_")]
    
    for bulk_dir in bulk_session_dirs:
        print(f"处理会话目录: {bulk_dir.name}")
        
        # 重置登录状态文件
        login_status_file = bulk_dir / "login_status.json"
        try:
            with open(login_status_file, "w", encoding="utf-8") as f:
                json.dump({
                    "status": "initializing",
                    "timestamp": 0,
                    "username": "default_user",
                    "store_name": "bulk_messaging"
                }, f, indent=2)
            print(f"  ✅ 重置登录状态文件: {login_status_file}")
        except Exception as e:
            print(f"  ❌ 重置登录状态文件失败: {e}")
        
        # 清空群发请求文件
        bulk_request_file = bulk_dir / "bulk_send_requests.json"
        try:
            with open(bulk_request_file, "w", encoding="utf-8") as f:
                json.dump({}, f)
            print(f"  ✅ 清空群发请求文件: {bulk_request_file}")
        except Exception as e:
            print(f"  ❌ 清空群发请求文件失败: {e}")
        
        # 重置群发状态文件
        bulk_status_file = bulk_dir / "bulk_send_status.json"
        try:
            with open(bulk_status_file, "w", encoding="utf-8") as f:
                json.dump({
                    "status": "idle",
                    "timestamp": 0
                }, f, indent=2)
            print(f"  ✅ 重置群发状态文件: {bulk_status_file}")
        except Exception as e:
            print(f"  ❌ 重置群发状态文件失败: {e}")

def check_dependencies():
    """检查Node.js依赖"""
    print("\n=== 检查Node.js依赖 ===")
    
    whatsapp_dir = Path(__file__).parent / "chat" / "whatsapp"
    
    # 检查package.json
    package_json = whatsapp_dir / "package.json"
    if not package_json.exists():
        print("❌ package.json不存在")
        return False
    
    # 检查node_modules
    node_modules = whatsapp_dir / "node_modules"
    if not node_modules.exists():
        print("❌ node_modules目录不存在，需要运行 npm install")
        return False
    
    # 检查关键依赖
    required_modules = ["whatsapp-web.js", "qrcode-terminal"]
    missing_modules = []
    
    for module in required_modules:
        module_dir = node_modules / module
        if not module_dir.exists():
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少依赖模块: {', '.join(missing_modules)}")
        return False
    
    print("✅ Node.js依赖检查通过")
    return True

def install_dependencies():
    """安装Node.js依赖"""
    print("\n=== 安装Node.js依赖 ===")
    
    whatsapp_dir = Path(__file__).parent / "chat" / "whatsapp"
    
    try:
        os.chdir(whatsapp_dir)
        result = subprocess.run(['npm', 'install'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖安装成功")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装依赖时出错: {e}")
        return False

def test_node_script():
    """测试Node.js脚本是否可以正常启动"""
    print("\n=== 测试Node.js脚本 ===")
    
    whatsapp_dir = Path(__file__).parent / "chat" / "whatsapp"
    bulk_login_script = whatsapp_dir / "bulk_login.js"
    
    if not bulk_login_script.exists():
        print("❌ bulk_login.js脚本不存在")
        return False
    
    try:
        # 设置测试环境变量
        env = os.environ.copy()
        env["USERNAME"] = "test_user"
        env["STORE_NAME"] = "test_store"
        env["SESSION_DIR"] = "test_session"
        
        # 创建测试目录
        test_dir = whatsapp_dir / "store_data" / "test_session"
        test_dir.mkdir(parents=True, exist_ok=True)
        
        env["STORE_DATA_DIR"] = str(test_dir)
        env["LOGIN_STATUS_FILE"] = str(test_dir / "login_status.json")
        
        os.chdir(whatsapp_dir)
        
        # 启动脚本并快速终止（只测试是否能启动）
        process = subprocess.Popen(
            ['node', str(bulk_login_script)],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待2秒然后终止
        import time
        time.sleep(2)
        process.terminate()
        
        stdout, stderr = process.communicate(timeout=5)
        
        if "启动WhatsApp群发登录服务" in stdout:
            print("✅ Node.js脚本可以正常启动")
            return True
        else:
            print(f"❌ Node.js脚本启动异常")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试Node.js脚本时出错: {e}")
        return False
    finally:
        # 清理测试目录
        try:
            import shutil
            test_dir = whatsapp_dir / "store_data" / "test_session"
            if test_dir.exists():
                shutil.rmtree(test_dir)
        except:
            pass

def main():
    """主函数"""
    print("🔧 群发获客功能快速修复工具")
    print("=" * 50)
    
    # 步骤1: 终止现有进程
    kill_node_processes()
    
    # 步骤2: 重置状态文件
    reset_status_files()
    
    # 步骤3: 检查依赖
    if not check_dependencies():
        print("\n🔄 尝试安装依赖...")
        if not install_dependencies():
            print("❌ 依赖安装失败，请手动运行: cd chat/whatsapp && npm install")
            return
    
    # 步骤4: 测试脚本
    if not test_node_script():
        print("❌ Node.js脚本测试失败")
        return
    
    print("\n" + "=" * 50)
    print("✅ 修复完成！")
    print("\n📋 下一步操作:")
    print("1. 重新启动应用程序")
    print("2. 点击'重置状态'按钮")
    print("3. 点击'登录WhatsApp'按钮")
    print("4. 扫描QR码完成登录")
    print("5. 添加电话号码和消息内容")
    print("6. 点击'开始群发'按钮")

if __name__ == "__main__":
    main()
