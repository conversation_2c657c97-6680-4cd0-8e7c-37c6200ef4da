"""
WhatsApp聊天客服模块
负责连接WhatsApp Web，接收消息并自动回复
"""
import os
import sys
import json
import time
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 获取项目根目录
def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent.parent

# 确保导入路径包含项目根目录
project_root = str(get_project_root())
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入知识库搜索和模型API
from knowledge_system.src.knowledge_search import search_knowledge
from model.Qwen.qwen_api import get_qwen_response
from database.store_operations import StoreOperations
from database.context_operations import ContextOperations
from database.db_manager import DatabaseManager
from database.intent_operations import IntentOperations

# 导入聊天服务接口
from chat.chat_interface import ChatInterface
from chat.web.LLM_service import generate_LLM_reply
from chat.web.LLM_service import input_trans
from chat.whatsapp.intention_detection import analyze_customer_intent_hybrid
from abc import ABC, abstractmethod

# 全局回调函数，用于处理店铺状态异常
_store_status_callback = None

def register_store_status_callback(callback):
    """
    注册店铺状态异常回调函数
    
    Args:
        callback: 回调函数，接收店铺名称参数
    """
    global _store_status_callback
    _store_status_callback = callback
    
def handle_store_status_exception(store_name):
    """
    处理店铺状态异常
    
    Args:
        store_name: 店铺名称
    """
    global _store_status_callback
    if _store_status_callback:
        _store_status_callback(store_name)

class WhatsAppService(ChatInterface):
    """WhatsApp聊天客服服务类"""
    
    def __init__(self, store_name: str, username: str = None, aggregation_timeout: int = 10):
        """
        初始化WhatsApp客服服务
        
        Args:
            store_name: 店铺名称
            aggregation_timeout: 消息聚合超时时间（秒），默认10秒
        """
        # 初始化数据库操作对象，确保在调用父类初始化前设置
        self.whatsapp_process = None
        self.store_operations = StoreOperations()
        self.context_operations = ContextOperations()
        self.intent_operations = IntentOperations()
        
        # 存储用户名，用于生成唯一的存储目录
        self.username = username
        logger.info(f"初始化WhatsApp服务，店铺: {store_name}, 用户名: {username}")
        
        # 调用父类初始化方法，会触发_platform_initialize
        super().__init__(store_name, aggregation_timeout)
    
    def _platform_initialize(self) -> None:
        """
        平台特定的初始化操作
        """
        # 获取当前目录
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # Node.js脚本路径
        self.node_script_path = os.path.join(self.current_dir, "index.js")
        
        # 创建店铺特定的数据目录
        self.store_data_dir = os.path.join(self.current_dir, "store_data", self._get_safe_store_name())
        os.makedirs(self.store_data_dir, exist_ok=True)
        
        # 获取店铺信息
        self.store_info = self.store_operations.get_store_by_name(self.store_name)
        if not self.store_info:
            logger.error(f"未找到店铺: {self.store_name}")
            
    def _get_safe_store_name(self) -> str:
        """获取安全的店铺名称（用于文件名），使用用户名+店铺名作为唯一索引"""
        # 如果没有用户名，使用默认用户
        username = self.username or 'default_user'
        
        # 清理用户名和店铺名，去除非法字符
        safe_username = "".join([c for c in username if c.isalnum() or c in "_-"])
        safe_storename = "".join([c for c in self.store_name if c.isalnum() or c in "_-"])
        
        # 确保安全名称不为空
        if not safe_username:
            safe_username = f"user_{hash(username) % 10000}"
        if not safe_storename:
            safe_storename = f"store_{hash(self.store_name) % 10000}"
        
        # 组合用户名和店铺名作为唯一索引
        combined_name = f"{safe_username}_{safe_storename}"
        logger.info(f"生成存储目录名称: {combined_name} (用户: {username}, 店铺: {self.store_name})")
        
        return combined_name
    
    def start(self) -> bool:
        """
        启动WhatsApp客服服务
        
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_running:
                logger.warning("WhatsApp客服服务已在运行")
                return True
                
            logger.info(f"正在启动WhatsApp客服服务，店铺: {self.store_name}")
            
            # 创建店铺特定的进程间通信文件
            ipc_file = os.path.join(self.store_data_dir, "whatsapp_messages.json")
            
            # 清空旧的消息通信文件
            with open(ipc_file, "w", encoding="utf-8") as f:
                json.dump([], f)
            
            # 确保Node.js脚本和依赖存在
            if not os.path.exists(self.node_script_path):
                logger.error(f"未找到WhatsApp服务脚本: {self.node_script_path}")
                return False
            
            # 设置环境变量传递店铺信息
            env = os.environ.copy()
            env["STORE_NAME"] = self.store_name
            env["IPC_FILE"] = ipc_file
            env["STORE_DATA_DIR"] = self.store_data_dir
            
            # 直接使用Node.js子进程执行JavaScript代码
            try:
                # 确保当前工作目录正确
                os.chdir(self.current_dir)
                
                # 使用subprocess启动Node.js进程
                import subprocess
                
                # 构建命令行
                node_cmd = ['node', self.node_script_path]
                
                # 在Windows环境中使用CREATE_NO_WINDOW标志
                if sys.platform == 'win32':
                    # 导入Windows特定API
                    try:
                        import win32process
                        import win32con
                        creationflags = win32process.CREATE_NO_WINDOW
                    except ImportError:
                        creationflags = 0x08000000  # CREATE_NO_WINDOW
                else:
                    creationflags = 0
                
                # 启动Node.js进程
                self.whatsapp_process = subprocess.Popen(
                    node_cmd,
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=False,  # 设置为False以获取原始字节
                    creationflags=creationflags
                )
                
                logger.info(f"已启动Node.js进程，PID: {self.whatsapp_process.pid}")
                
                # 创建线程读取输出
                def read_output():
                    while True:
                        try:
                            line = self.whatsapp_process.stdout.readline()
                            if not line and self.whatsapp_process.poll() is not None:
                                break
                            if line:
                                # 使用UTF-8解码输出
                                decoded_line = line.decode('utf-8', errors='replace').strip()
                                logger.info(f"WhatsApp进程输出: {decoded_line}")
                        except Exception as e:
                            logger.error(f"读取WhatsApp进程输出错误: {str(e)}")
                
                # 创建线程读取错误输出
                def read_error():
                    while True:
                        try:
                            line = self.whatsapp_process.stderr.readline()
                            if not line and self.whatsapp_process.poll() is not None:
                                break
                            if line:
                                # 使用UTF-8解码错误输出
                                decoded_line = line.decode('utf-8', errors='replace').strip()
                                logger.error(f"WhatsApp进程错误: {decoded_line}")
                        except Exception as e:
                            logger.error(f"读取WhatsApp进程错误输出错误: {str(e)}")
                
                # 启动输出读取线程
                stdout_thread = threading.Thread(target=read_output)
                stdout_thread.daemon = True
                stdout_thread.start()
                
                stderr_thread = threading.Thread(target=read_error)
                stderr_thread.daemon = True
                stderr_thread.start()
                
            except Exception as e:
                logger.error(f"启动Node.js进程时出错: {str(e)}")
                return False
            
            # 启动消息监控线程
            self.is_running = True
            self._start_message_monitor(ipc_file)
            
            logger.info(f"WhatsApp客服服务已启动，店铺: {self.store_name}")
            return True
        except Exception as e:
            logger.exception(f"启动WhatsApp客服服务时出错: {str(e)}")
            self.stop()
            return False
    
    def stop(self) -> bool:
        """
        停止WhatsApp客服服务
        
        Returns:
            bool: 停止是否成功
        """
        try:
            # 处理subprocess.Popen进程
            if self.whatsapp_process and self.whatsapp_process.poll() is None:  # poll()返回None表示进程仍在运行
                # 标记服务为停止状态
                self.is_running = False
                
                # 终止进程
                logger.info(f"正在终止WhatsApp进程，PID: {self.whatsapp_process.pid}")
                self.whatsapp_process.terminate()
                
                # 给进程一些时间来自行退出
                try:
                    self.whatsapp_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 如果超时，强制结束进程
                    logger.warning("WhatsApp进程未能在5秒内终止，强制结束进程")
                    self.whatsapp_process.kill()
                
                self.whatsapp_process = None
            
            # 取消所有计时器
            with self.message_lock:
                for sender_id, timer in list(self.aggregation_timers.items()):
                    if timer and timer.is_alive():
                        # 无法直接停止线程，但可以移除引用
                        self.aggregation_timers[sender_id] = None
                
                # 清空消息缓冲
                self.message_buffer.clear()
                self.last_message_time.clear()
            
            self.is_running = False
            logger.info("WhatsApp客服服务已停止")
            return True
        except Exception as e:
            logger.exception(f"停止WhatsApp客服服务时出错: {str(e)}")
            return False
    
    def _start_message_monitor(self, ipc_file: str) -> None:
        """
        启动消息监控线程
        
        Args:
            ipc_file: 进程间通信文件路径
        """
        def monitor():
            last_position = 0
            
            while self.is_running:
                try:
                    # 检查文件是否存在且有内容
                    if os.path.exists(ipc_file) and os.path.getsize(ipc_file) > 0:
                        with open(ipc_file, "r", encoding="utf-8") as f:
                            try:
                                messages = json.load(f)
                                
                                # 处理自上次检查以来的新消息
                                if len(messages) > last_position:
                                    new_messages = messages[last_position:]
                                    last_position = len(messages)
                                    
                                    # 处理每条新消息
                                    for message in new_messages:
                                        # 不再为每个消息启动线程，而是将消息加入缓冲区
                                        self.buffer_message(message)
                            except json.JSONDecodeError:
                                # 文件可能正在被写入，跳过这次读取
                                pass
                    
                    # 暂停一秒再检查
                    time.sleep(1)
                except Exception as e:
                    logger.error(f"监控消息时出错: {str(e)}")
                    time.sleep(5)  # 出错后等待稍长时间再重试
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor)
        monitor_thread.daemon = True
        monitor_thread.start()
        logger.info("已启动消息监控线程")
    
    def _get_sender_id(self, message: Dict[str, Any]) -> str:
        """
        从消息中提取发送者ID
        
        Args:
            message: 平台特定的消息对象
            
        Returns:
            str: 发送者ID
        """
        return message.get("from", "")
    
    def _get_message_content(self, message: Dict[str, Any]) -> str:
        """
        从消息中提取内容
        
        Args:
            message: 平台特定的消息对象
            
        Returns:
            str: 消息内容
        """
        return message.get("body", "")
    
    def _combine_messages(self, messages: List[Dict[str, Any]]) -> str:
        """
        合并多条消息内容
        
        Args:
            messages: 消息列表
            
        Returns:
            str: 合并后的消息内容
        """
        return "".join([msg.get("body", "") for msg in messages])
    
    def _create_aggregated_message(self, sender_id: str, content: str) -> Dict[str, Any]:
        """
        创建聚合后的消息对象
        
        Args:
            sender_id: 发送者ID
            content: 合并后的消息内容
            
        Returns:
            Dict[str, Any]: 聚合后的消息对象
        """
        return {
            "from": sender_id,
            "body": content
        }
    
    def handle_platform_event(self, event: Any) -> None:
        """
        处理平台特定的事件
        
        Args:
            event: 平台特定的事件对象
        """
        # WhatsApp中暂无特定事件处理逻辑
        pass
    
    def process_message(self, message: Dict[str, Any]) -> None:
        """
        处理接收到的WhatsApp消息
        
        Args:
            message: 消息字典，包含from（发送者ID）和body（消息内容）
        """
        try:
            sender_id = message.get("from")
            message_body = message.get("body", "")
            
            if not sender_id or not message_body:
                logger.warning("收到无效消息")
                return
            
            logger.info(f"处理来自 {sender_id} 的消息: {message_body}")

            # 添加店铺状态检查
            # 首先获取最新的店铺信息，确保拍照到当前状态
            latest_store_info = self.store_operations.get_store_by_name(self.store_name)
            if latest_store_info and latest_store_info.get('plg_status', 1) == 0:
                logger.warning(f"店铺{self.store_name}状态已关闭(plg_status=0)，拒绝处理消息")
                # 更新当前的店铺信息引用
                self.store_info = latest_store_info
                # 调用弹窗提示
                handle_store_status_exception(self.store_name)
                return
            
            # 1. 调用知识库搜索获取相关知识
            # knowledge_result = search_knowledge(message_body)

            # 判断是否处于人工介入状态
            if check_manual_intervention(self.store_name, sender_id):
                return
            
            # 2. 获取店铺提示词(prompt)
            store_prompt = self.store_info.get("plg_prompt", "")
            
            # 3. 从上下文表中获取聊天历史
            context_record = self.context_operations.get_context(
                "current_user", self.store_name, sender_id
            )
            context = context_record.get("context", "") if context_record else ""
            context_chz = context_record.get("context_trans", "") if context_record else ""

            # 4. 翻译用户消息
            message_body_chz = input_trans(message_body)
            
            # 5. 进行意图分析
            intent_result = analyze_customer_intent_hybrid(message_body_chz, context)
            logger.info(f"意图分析结果: {intent_result}")
            
            manual = 1 if intent_result.get("stage", "error") == "post-sales" else 0
            # 保存意图分析结果到数据库
            self.intent_operations.save_intent_analysis(
                self.store_name, 
                sender_id, 
                intent_result, 
                message_body,
                context,
                manual,  # manual参数，默认为0表示自动分析
                self.username  # 添加username参数，表示这条数据产生的wowcker账户
            )
            if manual:
                logger.info(f"用户{sender_id}处于人工介入模式，不处理自动回复")
                return

            response, reply_summary = generate_LLM_reply(message_body, context, store_prompt, message_chz=message_body_chz)
            response_chz = reply_summary.get("original_response", response)

            logger.info(f"千问回复: {response}")
            # 6. 更新积分(+1)
            # 每次处理消息前先获取最新的店铺信息，确保反映轮询器可能重置的积分
            self.store_info = self.store_operations.get_store_by_name(self.store_name)
            
            if self.store_info and "id" in self.store_info:
                # 使用事务原子性地更新积分
                result = self.store_operations.update_points_atomically(self.store_info["id"])
                
                if result:
                    logger.info(f"店铺积分已原子更新: {self.store_name}, 新积分: {result}")
                    
                    # 刷新店铺信息
                    self.store_info = self.store_operations.get_store_by_name(self.store_name)
                else:
                    logger.error(f"原子更新店铺积分失败: {self.store_name}")
            
            # 7. 更新上下文 - 使用完整聚合的问题和回答作为一次完整问答
            # 如果response是列表，则将其转换为字符串
            response_str = response
            if isinstance(response, list):
                response_str = "\n".join(response)  # 使用\n分隔每个回复
            # 统一使用与Test Chat相同的格式前缀，确保View Chat页面能正确解析
            new_context = f"{context}\n客户的历史提问：{message_body}\n你的历史回答：{response_str}" if context else f"客户的历史提问：{message_body}\n你的历史回答：{response_str}"
            # 翻译版上下文 - 统一使用与Test Chat相同的格式前缀
            new_context_trans = f"{context_chz}\n客户的历史提问（中）：{message_body_chz}\n你的历史回答（中）：{response_chz}" if context_chz else f"客户的历史提问（中）：{message_body_chz}\n你的历史回答（中）：{response_chz}"
            context_id = self.context_operations.add_context(
                "current_user", self.store_name, sender_id, new_context, new_context_trans
            )
            logger.info(f"已更新上下文记录(ID:{context_id})")
            
            # 8. 发送回复并记录
            self.send_reply(sender_id, response)
            
            # 记录最后发送的消息
            with self.message_lock:
                self.last_sent_messages[sender_id] = response
            
        except Exception as e:
            logger.exception(f"处理消息时出错: {str(e)}")
    
    def send_reply(self, recipient_id: str, message: Union[str, List[str]]) -> None:
        """
        向WhatsApp发送回复消息
        
        Args:
            recipient_id: 接收者ID
            message: 消息内容，可以是字符串或字符串列表
        """
        try:
            # 创建回复文件
            reply_file = os.path.join(self.store_data_dir, "whatsapp_replies.json")
            
            # 处理消息（字符串或列表）
            if isinstance(message, str):
                # 单个字符串消息的情况
                # 清空文件并写入单条消息
                with open(reply_file, "w", encoding="utf-8") as f:
                    reply_data = [{
                        "to": recipient_id,
                        "message": message,
                        "timestamp": time.time()
                    }]
                    json.dump(reply_data, f, ensure_ascii=False)
                
                # 等待JS处理文件
                time.sleep(1.5)  # 给JS机会读取和处理文件
                
                logger.info(f"已发送回复给 {recipient_id}: {message[:20]}...")
            elif isinstance(message, list):
                if not message:  # 空列表情况
                    return
                    
                # 列表消息的情况
                if len(message) == 1:
                    # 只有一个元素的列表，按单个消息处理
                    with open(reply_file, "w", encoding="utf-8") as f:
                        reply_data = [{
                            "to": recipient_id,
                            "message": message[0],
                            "timestamp": time.time()
                        }]
                        json.dump(reply_data, f, ensure_ascii=False)
                    
                    # 等待JS处理文件
                    time.sleep(1.5)  # 给JS机会读取和处理文件
                    
                    logger.info(f"已发送回复给 {recipient_id}: {message[0][:20]}...")
                else:
                    # 多个元素的列表，依次处理每条消息，并保持3秒间隔
                    for i, msg in enumerate(message):
                        # 每次写入文件前先清空文件，确保只放入一条消息
                        with open(reply_file, "w", encoding="utf-8") as f:
                            reply_data = [{
                                "to": recipient_id,
                                "message": msg,
                                "timestamp": time.time()
                            }]
                            json.dump(reply_data, f, ensure_ascii=False)
                        
                        logger.info(f"已发送第 {i+1}/{len(message)} 条回复给 {recipient_id}: {msg[:20]}...")
                        
                        # 等待JS处理文件
                        time.sleep(1.5)  # 给JS机会读取和处理文件
                        
                        # 多条消息时添加额外的间隔
                        if i < len(message) - 1:
                            time.sleep(3.5)  # 与前面的等待时间累加成大约5秒
                    
                    logger.info(f"已完成发送全部 {len(message)} 条回复给 {recipient_id}")
            
        except Exception as e:
            logger.exception(f"发送回复时出错: {str(e)}")

# 处理聊天测试消息的函数
def handle_test_message(store_name: str, sender_id: str, message_content: str) -> str:
    """
    处理测试聊天消息，返回回复内容
    
    Args:
        store_name: 店铺名称
        sender_id: 发送者ID（聊天ID）
        message_content: 消息内容
        
    Returns:
        str: 回复内容
    """
    username = "test_user"
    try:
        # 初始化数据库操作
        store_operations = StoreOperations()
        context_operations = ContextOperations()
        intent_operations = IntentOperations()
        
        # 获取店铺信息
        store_info = store_operations.get_store_by_name(store_name)
        if not store_info:
            return f"找不到店铺: {store_name}"
        
        # 检查店铺状态，如果状态为0（关闭或付款逾期），则返回None
        # 这里返回None而不是错误信息，因为在测试聊天界面我们已经用弹窗显示错误
        if store_info.get('plg_status', 1) == 0:
            logger.warning(f"店铺{store_name}状态已关闭(plg_status=0)，拒绝处理消息")
            return None
        
        # 判断是否处于人工介入状态
        if check_manual_intervention(store_name, sender_id):
            return

        
        # 获取店铺提示词
        store_prompt = store_info.get("plg_prompt", "")
        
        # 获取聊天历史上下文
        context_record = context_operations.get_context(
            "current_user", store_name, sender_id
        )
        context = context_record.get("context", "") if context_record else ""
        context_chz = context_record.get("context_trans", "") if context_record else ""

        # 4. 翻译用户消息
        message_content_chz = input_trans(message_content)

        # 5. 进行意图分析
        intent_result = analyze_customer_intent_hybrid(message_content_chz, context)
        logger.info(f"意图分析结果: {intent_result}")
        
        manual = 1 if intent_result.get("stage", "error") == "post-sales" else 0
        # 6. 保存意图分析结果到数据库
        intent_operations.save_intent_analysis(
            store_name, 
            sender_id, 
            intent_result, 
            message_content,
            context,
            manual,  # manual参数，默认为0表示自动分析
            username  # 添加username参数，表示这条数据产生的wowcker账户
        )
        if manual:
            logger.info(f"用户{sender_id}处于人工介入模式，不处理自动回复")
            return
        
        # 生成回复
        response, reply_summary = generate_LLM_reply(message_content, context, store_prompt, message_chz=message_content_chz)
        response_chz = reply_summary.get("original_response", response)
        logger.info(f"测试消息千问回复: {response}")
        
        # 统一使用与WhatsApp消息相同的格式前缀，确保View Chat页面能正确解析
        new_context = f"{context}\n客户的历史提问：{message_content}\n你的历史回答：{response}" if context else f"客户的历史提问：{message_content}\n你的历史回答：{response}"
        # 翻译版上下文 - 统一使用相同的格式前缀
        new_context_trans = f"{context_chz}\n客户的历史提问（中）：{message_content_chz}\n你的历史回答（中）：{response_chz}" if context_chz else f"客户的历史提问（中）：{message_content_chz}\n你的历史回答（中）：{response_chz}"
        context_id = context_operations.add_context(
            "current_user", store_name, sender_id, new_context, new_context_trans
        )
        
        # 更新店铺积分(+1)
        if store_info and "id" in store_info:
            # 使用事务原子性地更新积分
            result = store_operations.update_points_atomically(store_info["id"])
            
            if result:
                logger.info(f"测试聊天店铺积分已原子更新: {store_name}, 新积分: {result}")
            else:
                logger.warning(f"测试聊天店铺积分更新失败: {store_name}")
        
        # 返回生成的回复
        if isinstance(response, list):
            return "\n".join(response)  # 如果是列表，转换为字符串
        return response
        
    except Exception as e:
        logger.exception(f"处理测试消息时出错: {str(e)}")
        return f"处理消息失败: {str(e)}"


# 检查人工介入模式
def check_manual_intervention(store_name: str, sender_id: str) -> bool:
    """
    检查用户是否处于人工介入模式
    
    Args:
        store_name: 店铺名称
        sender_id: 发送者ID
        
    Returns:
        bool: True表示处于人工介入模式，False表示非人工介入模式
    """
    from database.intent_operations import IntentOperations
    import logging
    
    logger = logging.getLogger(__name__)
    intent_operations = IntentOperations()
    
    try:
        # 检查数据库中是否存在对应的intent_analysis记录
        conn = intent_operations.db_manager.connect()
        if conn:
            query = """
            SELECT manual FROM intent_analysis 
            WHERE store_name = ? AND sender_id = ? 
            ORDER BY created_at DESC LIMIT 1
            """
            intent_operations.db_manager.cursor.execute(query, (store_name, sender_id))
            record = intent_operations.db_manager.cursor.fetchone()
            
            # 如果记录存在且manual值为1，则处于人工介入模式
            if record and record[0] == 1:
                logger.info(f"用户 {sender_id} 处于人工介入模式，不处理自动回复")
                intent_operations.db_manager.close()
                return True
            
            intent_operations.db_manager.close()
            return False
    except Exception as e:
        logger.error(f"检查人工介入状态时出错: {str(e)}")
        # 出错时默认为非人工介入模式
        return False


# 测试代码
if __name__ == "__main__":
    service = WhatsAppService("测试店铺")
    service.start()
    
    try:
        # 保持运行直到手动中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        service.stop()
