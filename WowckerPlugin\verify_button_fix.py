#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证按钮修复的脚本 - 不依赖PySide6
"""

import sys
import os
import ast
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_controller_code():
    """检查控制器代码是否包含修复内容"""
    controller_path = Path(__file__).parent / "PyOneDark_Qt_Widgets_Modern_GUI-master" / "gui" / "core" / "bulk_customer_acquisition_controller.py"
    
    if not controller_path.exists():
        logger.error(f"控制器文件不存在: {controller_path}")
        return False
    
    try:
        with open(controller_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复内容
        checks = [
            ("_initialize_ui_components", "UI组件初始化检查方法"),
            ("_test_button_connections", "按钮连接测试方法"),
            ("logger.info", "详细日志记录"),
            ("add_phone_number_dialog", "添加电话号码对话框方法"),
            ("start_whatsapp_login", "WhatsApp登录方法"),
            ("connect_signals", "信号连接方法"),
            ("missing_components", "缺失组件检查"),
            ("signal_connections", "信号连接列表"),
            ("WhatsApp登录按钮被点击", "登录按钮点击日志"),
            ("添加电话号码按钮被点击", "添加号码按钮点击日志")
        ]
        
        results = []
        for check_item, description in checks:
            if check_item in content:
                logger.info(f"✓ 找到 {description}")
                results.append(True)
            else:
                logger.error(f"✗ 缺失 {description}")
                results.append(False)
        
        # 检查方法定义
        method_checks = [
            "def _initialize_ui_components(self):",
            "def _test_button_connections(self):",
            "def add_phone_number_dialog(self):",
            "def start_whatsapp_login(self):",
            "def connect_signals(self):"
        ]
        
        for method in method_checks:
            if method in content:
                logger.info(f"✓ 方法定义存在: {method}")
                results.append(True)
            else:
                logger.error(f"✗ 方法定义缺失: {method}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        logger.error(f"检查控制器代码时出错: {e}")
        return False

def check_ui_components():
    """检查UI组件定义"""
    ui_path = Path(__file__).parent / "PyOneDark_Qt_Widgets_Modern_GUI-master" / "gui" / "uis" / "pages" / "ui_main_pages.py"
    
    if not ui_path.exists():
        logger.error(f"UI文件不存在: {ui_path}")
        return False
    
    try:
        with open(ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键UI组件
        ui_components = [
            ("self.add_phone_btn = QPushButton", "添加电话号码按钮"),
            ("self.whatsapp_login_btn = QPushButton", "WhatsApp登录按钮"),
            ("self.bulk_send_btn = QPushButton", "群发消息按钮"),
            ("self.login_status_indicator = QLabel", "登录状态指示器"),
            ("self.phone_numbers_table = QTableWidget", "电话号码表格"),
            ("self.message_input_field = QTextEdit", "消息输入框")
        ]
        
        results = []
        for component, description in ui_components:
            if component in content:
                logger.info(f"✓ UI组件存在: {description}")
                results.append(True)
            else:
                logger.error(f"✗ UI组件缺失: {description}")
                results.append(False)
        
        # 检查对象名称设置
        object_names = [
            'setObjectName(u"add_phone_btn")',
            'setObjectName(u"whatsapp_login_btn")',
            'setObjectName(u"bulk_send_btn")'
        ]
        
        for obj_name in object_names:
            if obj_name in content:
                logger.info(f"✓ 对象名称设置: {obj_name}")
                results.append(True)
            else:
                logger.error(f"✗ 对象名称缺失: {obj_name}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        logger.error(f"检查UI组件时出错: {e}")
        return False

def check_main_window_integration():
    """检查主窗口集成"""
    main_path = Path(__file__).parent / "PyOneDark_Qt_Widgets_Modern_GUI-master" / "main.py"
    
    if not main_path.exists():
        logger.error(f"主窗口文件不存在: {main_path}")
        return False
    
    try:
        with open(main_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查控制器初始化
        integration_checks = [
            ("BulkCustomerAcquisitionController", "控制器类导入"),
            ("self.bulk_acquisition_controller", "控制器实例创建"),
            ("btn_bulk_customer_acquisition", "群发获客按钮处理")
        ]
        
        results = []
        for check_item, description in integration_checks:
            if check_item in content:
                logger.info(f"✓ 主窗口集成: {description}")
                results.append(True)
            else:
                logger.error(f"✗ 主窗口集成缺失: {description}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        logger.error(f"检查主窗口集成时出错: {e}")
        return False

def check_whatsapp_modules():
    """检查WhatsApp模块"""
    whatsapp_dir = Path(__file__).parent / "chat" / "whatsapp"
    
    required_files = [
        "bulk_login_manager.py",
        "bulk_login.js", 
        "bulk_sender.py"
    ]
    
    results = []
    for file_name in required_files:
        file_path = whatsapp_dir / file_name
        if file_path.exists():
            logger.info(f"✓ WhatsApp模块存在: {file_name}")
            results.append(True)
        else:
            logger.error(f"✗ WhatsApp模块缺失: {file_name}")
            results.append(False)
    
    return all(results)

def main():
    """主验证函数"""
    logger.info("开始验证按钮修复...")
    logger.info("=" * 50)
    
    tests = [
        ("控制器代码检查", check_controller_code),
        ("UI组件检查", check_ui_components),
        ("主窗口集成检查", check_main_window_integration),
        ("WhatsApp模块检查", check_whatsapp_modules)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                logger.info(f"✓ {test_name} 通过")
            else:
                logger.error(f"✗ {test_name} 失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"验证结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有检查通过！按钮修复应该已经完成。")
        logger.info("\n下一步:")
        logger.info("1. 启动应用程序")
        logger.info("2. 点击左侧的'群发获客'按钮")
        logger.info("3. 尝试点击'添加号码'和'登录WhatsApp'按钮")
        logger.info("4. 查看控制台日志输出")
        return True
    else:
        logger.error("⚠️ 部分检查失败，可能需要进一步修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
