# -*- coding: utf-8 -*-

"""
批量消息发送服务
整合电话号码管理、WhatsApp登录和消息发送功能
"""

import os
import sys
import logging
from typing import List, Dict, Any, Callable, Optional

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from chat.whatsapp.bulk_login_manager import WhatsAppBulkLoginManager
    from chat.whatsapp.bulk_sender import WhatsAppBulkSender
    from database.telenumber_operations import TeleNumberOperations
    from utils.phone_number_utils import PhoneNumberUtils
    from utils.login_state_manager import LoginStateManager
except ImportError as e:
    logging.error(f"导入模块失败: {e}")

logger = logging.getLogger(__name__)

class BulkMessageService:
    """批量消息发送服务"""
    
    def __init__(self, username: str = "default_user", store_name: str = "bulk_messaging"):
        """
        初始化批量消息服务
        
        Args:
            username: 用户名
            store_name: 店铺名称
        """
        self.username = username
        self.store_name = store_name
        
        # 初始化组件
        self.login_manager = None
        self.bulk_sender = None
        self.phone_operations = TeleNumberOperations()
        self.login_state_manager = LoginStateManager(username, store_name)
        
        # 回调函数
        self.login_status_callbacks = []
        self.qr_code_callbacks = []
        self.send_progress_callbacks = []
        self.send_completion_callbacks = []
        self.error_callbacks = []
        
        # 初始化WhatsApp组件
        self._init_whatsapp_components()
        
        logger.info(f"初始化批量消息服务: {username}@{store_name}")
    
    def _init_whatsapp_components(self):
        """初始化WhatsApp相关组件"""
        try:
            # 初始化登录管理器
            self.login_manager = WhatsAppBulkLoginManager(self.username, self.store_name)
            self.login_manager.set_callbacks(
                login_status_callback=self._on_login_status_changed,
                qr_code_callback=self._on_qr_code_received,
                error_callback=self._on_login_error
            )
            
            # 初始化批量发送器
            self.bulk_sender = WhatsAppBulkSender(self.login_manager)
            self.bulk_sender.set_callbacks(
                progress_callback=self._on_send_progress,
                completion_callback=self._on_send_completion,
                error_callback=self._on_send_error
            )
            
            logger.info("WhatsApp组件初始化成功")
            
        except Exception as e:
            logger.error(f"初始化WhatsApp组件失败: {e}")
    
    # 回调管理
    def add_login_status_callback(self, callback: Callable[[bool], None]):
        """添加登录状态回调"""
        if callback not in self.login_status_callbacks:
            self.login_status_callbacks.append(callback)
    
    def add_qr_code_callback(self, callback: Callable[[str], None]):
        """添加QR码回调"""
        if callback not in self.qr_code_callbacks:
            self.qr_code_callbacks.append(callback)
    
    def add_send_progress_callback(self, callback: Callable[[int, str, str], None]):
        """添加发送进度回调"""
        if callback not in self.send_progress_callbacks:
            self.send_progress_callbacks.append(callback)
    
    def add_send_completion_callback(self, callback: Callable[[bool, int, int, List], None]):
        """添加发送完成回调"""
        if callback not in self.send_completion_callbacks:
            self.send_completion_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[str], None]):
        """添加错误回调"""
        if callback not in self.error_callbacks:
            self.error_callbacks.append(callback)
    
    # 内部回调处理
    def _on_login_status_changed(self, is_logged_in: bool):
        """登录状态变化处理"""
        # 保存登录状态
        session_info = self.login_manager.get_session_info() if self.login_manager else {}
        self.login_state_manager.save_login_state(is_logged_in, session_info)
        
        # 通知回调
        for callback in self.login_status_callbacks:
            try:
                callback(is_logged_in)
            except Exception as e:
                logger.error(f"登录状态回调执行失败: {e}")
    
    def _on_qr_code_received(self, qr_code: str):
        """QR码接收处理"""
        for callback in self.qr_code_callbacks:
            try:
                callback(qr_code)
            except Exception as e:
                logger.error(f"QR码回调执行失败: {e}")
    
    def _on_login_error(self, error_message: str):
        """登录错误处理"""
        for callback in self.error_callbacks:
            try:
                callback(f"登录错误: {error_message}")
            except Exception as e:
                logger.error(f"错误回调执行失败: {e}")
    
    def _on_send_progress(self, progress: int, status: str, current_phone: str):
        """发送进度处理"""
        for callback in self.send_progress_callbacks:
            try:
                callback(progress, status, current_phone)
            except Exception as e:
                logger.error(f"发送进度回调执行失败: {e}")
    
    def _on_send_completion(self, success: bool, sent_count: int, total_count: int, errors: List):
        """发送完成处理"""
        for callback in self.send_completion_callbacks:
            try:
                callback(success, sent_count, total_count, errors)
            except Exception as e:
                logger.error(f"发送完成回调执行失败: {e}")
    
    def _on_send_error(self, error_message: str):
        """发送错误处理"""
        for callback in self.error_callbacks:
            try:
                callback(f"发送错误: {error_message}")
            except Exception as e:
                logger.error(f"错误回调执行失败: {e}")
    
    # 登录相关方法
    def start_login(self) -> bool:
        """开始登录"""
        if not self.login_manager:
            return False
        return self.login_manager.start_login()
    
    def logout(self) -> bool:
        """退出登录"""
        if not self.login_manager:
            return False
        
        success = self.login_manager.logout()
        if success:
            self.login_state_manager.save_login_state(False)
        return success
    
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        if self.login_manager:
            return self.login_manager.check_login_status()
        return self.login_state_manager.is_logged_in()
    
    def is_login_in_progress(self) -> bool:
        """检查是否正在登录"""
        if not self.login_manager:
            return False
        return self.login_manager.is_login_in_progress()
    
    # 电话号码管理
    def add_phone_number(self, phone_number: str, display_format: str = None) -> bool:
        """
        添加电话号码
        
        Args:
            phone_number: WhatsApp格式的电话号码
            display_format: 显示格式（可选）
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 如果没有提供显示格式，则从WhatsApp格式转换
            if not display_format:
                display_format = PhoneNumberUtils.convert_whatsapp_to_display(phone_number)
            
            # 添加到数据库
            result = self.phone_operations.add_phone_number(
                phone_number, self.username, self.store_name
            )
            
            if result > 0:
                logger.info(f"成功添加电话号码: {display_format}")
                return True
            else:
                logger.warning(f"电话号码已存在: {display_format}")
                return False
                
        except Exception as e:
            logger.error(f"添加电话号码失败: {e}")
            return False
    
    def get_phone_numbers(self) -> List[Dict[str, Any]]:
        """获取所有电话号码"""
        try:
            numbers = self.phone_operations.get_phone_numbers_by_user(
                self.username, self.store_name
            )
            
            # 转换为显示格式
            for number in numbers:
                phone_number = number.get('phone_number', '')
                number['display_format'] = PhoneNumberUtils.convert_whatsapp_to_display(phone_number)
            
            return numbers
            
        except Exception as e:
            logger.error(f"获取电话号码失败: {e}")
            return []
    
    def remove_phone_number(self, phone_number: str) -> bool:
        """删除电话号码"""
        try:
            success = self.phone_operations.delete_phone_number(
                phone_number, self.username, self.store_name
            )
            if success:
                logger.info(f"成功删除电话号码: {phone_number}")
            return success
        except Exception as e:
            logger.error(f"删除电话号码失败: {e}")
            return False
    
    def clear_all_phone_numbers(self) -> bool:
        """清空所有电话号码"""
        try:
            numbers = self.get_phone_numbers()
            success_count = 0
            
            for number in numbers:
                if self.remove_phone_number(number['phone_number']):
                    success_count += 1
            
            logger.info(f"清空电话号码完成: {success_count}/{len(numbers)}")
            return success_count == len(numbers)
            
        except Exception as e:
            logger.error(f"清空电话号码失败: {e}")
            return False
    
    # 消息发送
    def send_bulk_message(self, message: str, delay_seconds: int = 3) -> bool:
        """
        发送批量消息
        
        Args:
            message: 消息内容
            delay_seconds: 发送间隔（秒）
            
        Returns:
            bool: 是否成功开始发送
        """
        try:
            # 检查登录状态
            if not self.is_logged_in():
                logger.error("未登录，无法发送消息")
                return False
            
            # 获取电话号码列表
            phone_numbers = self.get_phone_numbers()
            if not phone_numbers:
                logger.error("没有电话号码，无法发送消息")
                return False
            
            # 提取WhatsApp格式的号码
            whatsapp_numbers = [num['phone_number'] for num in phone_numbers]
            
            # 转换延迟为毫秒
            delay_ms = delay_seconds * 1000
            
            # 发送消息
            if self.bulk_sender:
                return self.bulk_sender.send_bulk_messages(whatsapp_numbers, message, delay_ms)
            else:
                logger.error("批量发送器未初始化")
                return False
                
        except Exception as e:
            logger.error(f"发送批量消息失败: {e}")
            return False
    
    def stop_sending(self) -> bool:
        """停止发送"""
        if self.bulk_sender:
            return self.bulk_sender.stop_sending()
        return False
    
    def is_sending_in_progress(self) -> bool:
        """检查是否正在发送"""
        if self.bulk_sender:
            return self.bulk_sender.is_sending_in_progress()
        return False
    
    def get_sending_status(self) -> Dict[str, Any]:
        """获取发送状态"""
        if self.bulk_sender:
            return self.bulk_sender.get_sending_status()
        return {"status": "unknown"}
    
    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        info = {
            "username": self.username,
            "store_name": self.store_name,
            "is_logged_in": self.is_logged_in(),
            "is_login_in_progress": self.is_login_in_progress(),
            "is_sending": self.is_sending_in_progress(),
            "phone_count": len(self.get_phone_numbers())
        }
        
        if self.login_manager:
            info.update(self.login_manager.get_session_info())
        
        return info
