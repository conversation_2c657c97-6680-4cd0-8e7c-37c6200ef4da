"""
WhatsApp群发登录管理器
负责管理群发功能的WhatsApp登录状态
"""

import os
import sys
import json
import time
import logging
import threading
import subprocess
from pathlib import Path
from typing import Optional, Callable, Dict, Any

logger = logging.getLogger(__name__)

class WhatsAppBulkLoginManager:
    """WhatsApp群发登录管理器"""
    
    def __init__(self, username: str, store_name: str = "bulk_messaging"):
        """
        初始化登录管理器
        
        Args:
            username: 用户名
            store_name: 店铺名称（默认为bulk_messaging）
        """
        self.username = username
        self.store_name = store_name
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 生成安全的存储目录名称
        self.session_dir_name = self._get_safe_session_name()
        self.store_data_dir = os.path.join(self.current_dir, "store_data", self.session_dir_name)
        os.makedirs(self.store_data_dir, exist_ok=True)
        
        # 状态管理
        self.is_logged_in = False
        self.is_logging_in = False
        self.login_process = None
        self.status_monitor_thread = None
        self.should_monitor = False
        
        # 回调函数
        self.login_status_callback = None
        self.qr_code_callback = None
        self.error_callback = None
        
        # 文件路径
        self.login_status_file = os.path.join(self.store_data_dir, "login_status.json")
        self.node_script_path = os.path.join(self.current_dir, "bulk_login.js")
        
        logger.info(f"初始化群发登录管理器: 用户={username}, 店铺={store_name}, 会话目录={self.session_dir_name}")
    
    def _get_safe_session_name(self) -> str:
        """获取安全的会话目录名称"""
        # 清理用户名和店铺名，去除非法字符
        safe_username = "".join([c for c in self.username if c.isalnum() or c in "_-"])
        safe_storename = "".join([c for c in self.store_name if c.isalnum() or c in "_-"])
        
        # 确保安全名称不为空
        if not safe_username:
            safe_username = f"user_{hash(self.username) % 10000}"
        if not safe_storename:
            safe_storename = f"store_{hash(self.store_name) % 10000}"
        
        # 组合用户名和店铺名作为唯一索引，添加bulk前缀以区分
        combined_name = f"bulk_{safe_username}_{safe_storename}"
        return combined_name
    
    def set_callbacks(self, login_status_callback: Callable = None, 
                     qr_code_callback: Callable = None, 
                     error_callback: Callable = None):
        """
        设置回调函数
        
        Args:
            login_status_callback: 登录状态变化回调 (is_logged_in: bool)
            qr_code_callback: QR码生成回调 (qr_code: str)
            error_callback: 错误回调 (error_message: str)
        """
        self.login_status_callback = login_status_callback
        self.qr_code_callback = qr_code_callback
        self.error_callback = error_callback
    
    def start_login(self) -> bool:
        """
        启动登录流程
        
        Returns:
            bool: 启动是否成功
        """
        if self.is_logged_in:
            logger.warning("已经登录，无需重复登录")
            return True
        
        if self.is_logging_in:
            logger.warning("正在登录中，请等待")
            return False
        
        try:
            logger.info("开始启动WhatsApp群发登录流程")
            self.is_logging_in = True
            
            # 清空登录状态文件
            self._clear_status_file()
            
            # 检查Node.js脚本是否存在
            if not os.path.exists(self.node_script_path):
                logger.error(f"未找到登录脚本: {self.node_script_path}")
                self._handle_error("登录脚本不存在")
                return False
            
            # 设置环境变量
            env = os.environ.copy()
            env["USERNAME"] = self.username
            env["STORE_NAME"] = self.store_name
            env["SESSION_DIR"] = self.session_dir_name
            env["STORE_DATA_DIR"] = self.store_data_dir
            env["LOGIN_STATUS_FILE"] = self.login_status_file
            
            # 启动Node.js进程
            try:
                os.chdir(self.current_dir)
                
                # Windows环境配置
                if sys.platform == 'win32':
                    try:
                        import win32process
                        creationflags = win32process.CREATE_NO_WINDOW
                    except ImportError:
                        creationflags = 0x08000000  # CREATE_NO_WINDOW
                else:
                    creationflags = 0
                
                # 启动登录进程
                self.login_process = subprocess.Popen(
                    ['node', self.node_script_path],
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,  # 改为True以正确处理文本输出
                    bufsize=1,  # 行缓冲
                    universal_newlines=True,
                    creationflags=creationflags
                )
                
                logger.info(f"已启动登录进程，PID: {self.login_process.pid}")
                
                # 启动状态监控
                self._start_status_monitor()
                
                return True
                
            except Exception as e:
                logger.error(f"启动登录进程时出错: {str(e)}")
                self._handle_error(f"启动登录进程失败: {str(e)}")
                return False
                
        except Exception as e:
            logger.exception(f"启动登录流程时出错: {str(e)}")
            self._handle_error(f"启动登录失败: {str(e)}")
            return False
        finally:
            if not self.login_process:
                self.is_logging_in = False
    
    def _clear_status_file(self):
        """清空状态文件"""
        try:
            with open(self.login_status_file, "w", encoding="utf-8") as f:
                json.dump({"status": "initializing"}, f)
        except Exception as e:
            logger.error(f"清空状态文件时出错: {str(e)}")
    
    def _start_status_monitor(self):
        """启动状态监控线程"""
        self.should_monitor = True
        self.status_monitor_thread = threading.Thread(target=self._monitor_status)
        self.status_monitor_thread.daemon = True
        self.status_monitor_thread.start()
        logger.info("已启动登录状态监控线程")
    
    def _monitor_status(self):
        """监控登录状态"""
        last_status = None
        last_timestamp = 0

        while self.should_monitor:
            try:
                if os.path.exists(self.login_status_file):
                    with open(self.login_status_file, "r", encoding="utf-8") as f:
                        try:
                            status_data = json.load(f)

                            # 只处理状态变化或新的时间戳
                            current_status = status_data.get("status", "unknown")
                            current_timestamp = status_data.get("timestamp", 0)

                            if (current_status != last_status or
                                current_timestamp != last_timestamp):
                                self._handle_status_update(status_data)
                                last_status = current_status
                                last_timestamp = current_timestamp

                        except json.JSONDecodeError:
                            # 文件可能正在被写入，跳过这次读取
                            pass

                # 检查进程是否还在运行
                if self.login_process and self.login_process.poll() is not None:
                    # 进程已结束
                    if not self.is_logged_in:
                        self._handle_error("登录进程意外结束")
                    break

                # 如果已经登录成功，减少监控频率
                if self.is_logged_in:
                    time.sleep(5)  # 已登录时每5秒检查一次
                else:
                    time.sleep(1)  # 登录中时每秒检查一次

            except Exception as e:
                logger.error(f"监控登录状态时出错: {str(e)}")
                time.sleep(5)
    
    def _handle_status_update(self, status_data: Dict[str, Any]):
        """处理状态更新"""
        status = status_data.get("status", "unknown")
        
        if status == "qr_code":
            # QR码生成
            qr_code = status_data.get("qr_code", "")
            logger.info("收到QR码，请扫描登录")
            if self.qr_code_callback:
                self.qr_code_callback(qr_code)
        
        elif status == "authenticated":
            # 认证成功
            logger.info("WhatsApp认证成功")
        
        elif status == "ready":
            # 登录完成
            logger.info("WhatsApp登录完成，客户端已就绪")
            self.is_logged_in = True
            self.is_logging_in = False
            if self.login_status_callback:
                self.login_status_callback(True)
        
        elif status == "error":
            # 登录错误
            error_message = status_data.get("message", "未知错误")
            logger.error(f"登录过程中出错: {error_message}")
            self._handle_error(error_message)
        
        elif status == "disconnected":
            # 连接断开
            logger.warning("WhatsApp连接已断开")
            self.is_logged_in = False
            if self.login_status_callback:
                self.login_status_callback(False)
    
    def _handle_error(self, error_message: str):
        """处理错误"""
        self.is_logging_in = False
        self.is_logged_in = False
        if self.error_callback:
            self.error_callback(error_message)
    
    def logout(self) -> bool:
        """
        退出登录
        
        Returns:
            bool: 退出是否成功
        """
        try:
            logger.info("开始退出WhatsApp登录")
            
            # 停止监控
            self.should_monitor = False
            
            # 终止登录进程
            if self.login_process and self.login_process.poll() is None:
                logger.info(f"正在终止登录进程，PID: {self.login_process.pid}")
                self.login_process.terminate()
                
                try:
                    self.login_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("登录进程未能在5秒内终止，强制结束")
                    self.login_process.kill()
                
                self.login_process = None
            
            # 更新状态
            self.is_logged_in = False
            self.is_logging_in = False
            
            # 清理会话文件（可选）
            # self._clear_session_data()
            
            logger.info("WhatsApp登录已退出")
            
            if self.login_status_callback:
                self.login_status_callback(False)
            
            return True
            
        except Exception as e:
            logger.exception(f"退出登录时出错: {str(e)}")
            return False
    
    def check_login_status(self) -> bool:
        """
        检查当前登录状态

        Returns:
            bool: 是否已登录
        """
        # 首先检查内存中的状态
        if self.is_logged_in:
            return True

        # 然后检查状态文件
        try:
            if os.path.exists(self.login_status_file):
                with open(self.login_status_file, "r", encoding="utf-8") as f:
                    status_data = json.load(f)
                    status = status_data.get("status", "unknown")

                    # 如果状态文件显示已就绪，但内存状态不一致，则更新内存状态
                    if status == "ready" and not self.is_logged_in:
                        logger.info("从状态文件检测到登录成功，更新内存状态")
                        self.is_logged_in = True
                        self.is_logging_in = False
                        if self.login_status_callback:
                            self.login_status_callback(True)
                        return True

                    return status == "ready"
        except Exception as e:
            logger.error(f"检查登录状态文件时出错: {str(e)}")

        return self.is_logged_in
    
    def is_login_in_progress(self) -> bool:
        """
        检查是否正在登录中
        
        Returns:
            bool: 是否正在登录
        """
        return self.is_logging_in
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        获取会话信息
        
        Returns:
            Dict[str, Any]: 会话信息
        """
        return {
            "username": self.username,
            "store_name": self.store_name,
            "session_dir": self.session_dir_name,
            "is_logged_in": self.is_logged_in,
            "is_logging_in": self.is_logging_in,
            "store_data_dir": self.store_data_dir
        }
    
    def _clear_session_data(self):
        """清理会话数据（谨慎使用）"""
        try:
            session_dir = os.path.join(self.store_data_dir, "session")
            if os.path.exists(session_dir):
                import shutil
                shutil.rmtree(session_dir)
                logger.info("已清理会话数据")
        except Exception as e:
            logger.error(f"清理会话数据时出错: {str(e)}")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if hasattr(self, 'should_monitor'):
                self.should_monitor = False
            if hasattr(self, 'login_process') and self.login_process:
                if self.login_process.poll() is None:
                    self.login_process.terminate()
        except:
            pass
