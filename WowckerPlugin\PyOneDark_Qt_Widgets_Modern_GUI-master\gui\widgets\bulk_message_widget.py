# -*- coding: utf-8 -*-

"""
批量消息发送组件
用于群发获客页面的消息输入和发送功能
"""

import os
import sys
import logging
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QTextCursor
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QFrame, QMessageBox, QProgressBar, QSpinBox
)

logger = logging.getLogger(__name__)

class BulkMessageWidget(QWidget):
    """批量消息发送组件"""
    
    # 信号
    send_message_requested = Signal(str, int)  # (消息内容, 发送间隔)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_logged_in = False
        self.is_sending = False
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("批量消息发送")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #fff; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 消息输入区域
        message_frame = QFrame()
        message_frame.setStyleSheet("""
            QFrame {
                background-color: #2c313c;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        message_layout = QVBoxLayout(message_frame)
        message_layout.setContentsMargins(15, 15, 15, 15)
        message_layout.setSpacing(10)
        
        # 消息输入标签
        message_label = QLabel("消息内容:")
        message_label.setStyleSheet("color: #f8f8f2; font-size: 12pt; font-weight: bold;")
        message_layout.addWidget(message_label)
        
        # 消息输入框
        self.message_input = QTextEdit()
        self.message_input.setPlaceholderText("请输入要群发的消息内容...")
        self.message_input.setMinimumHeight(120)
        self.message_input.setMaximumHeight(200)
        self.message_input.setStyleSheet("""
            QTextEdit {
                background-color: #1b1e23;
                color: #f8f8f2;
                border: 1px solid #3c4454;
                border-radius: 6px;
                padding: 10px;
                font-size: 11pt;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid #5e7ce0;
            }
        """)
        self.message_input.textChanged.connect(self._on_message_changed)
        message_layout.addWidget(self.message_input)
        
        # 字符计数
        self.char_count_label = QLabel("0 / 1000 字符")
        self.char_count_label.setAlignment(Qt.AlignRight)
        self.char_count_label.setStyleSheet("color: #a0a0a0; font-size: 10pt;")
        message_layout.addWidget(self.char_count_label)
        
        layout.addWidget(message_frame)
        
        # 发送设置区域
        settings_frame = QFrame()
        settings_frame.setStyleSheet("""
            QFrame {
                background-color: #2c313c;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        settings_layout = QVBoxLayout(settings_frame)
        settings_layout.setContentsMargins(15, 15, 15, 15)
        settings_layout.setSpacing(10)
        
        # 发送设置标签
        settings_label = QLabel("发送设置:")
        settings_label.setStyleSheet("color: #f8f8f2; font-size: 12pt; font-weight: bold;")
        settings_layout.addWidget(settings_label)
        
        # 发送间隔设置
        interval_layout = QHBoxLayout()
        interval_label = QLabel("发送间隔:")
        interval_label.setStyleSheet("color: #f8f8f2; font-size: 11pt;")
        interval_label.setMinimumWidth(80)
        
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(1, 60)
        self.interval_spinbox.setValue(3)
        self.interval_spinbox.setSuffix(" 秒")
        self.interval_spinbox.setStyleSheet("""
            QSpinBox {
                background-color: #1b1e23;
                color: #f8f8f2;
                border: 1px solid #3c4454;
                border-radius: 6px;
                padding: 8px;
                font-size: 11pt;
                min-width: 100px;
            }
            QSpinBox:focus {
                border: 2px solid #5e7ce0;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #3c4454;
                border: none;
                width: 20px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #5e7ce0;
            }
        """)
        
        interval_info = QLabel("(建议3-5秒，避免被限制)")
        interval_info.setStyleSheet("color: #a0a0a0; font-size: 10pt;")
        
        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(self.interval_spinbox)
        interval_layout.addWidget(interval_info)
        interval_layout.addStretch()
        
        settings_layout.addLayout(interval_layout)
        layout.addWidget(settings_frame)
        
        # 发送状态区域
        self.status_frame = QFrame()
        self.status_frame.setStyleSheet("""
            QFrame {
                background-color: #2c313c;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        self.status_frame.setVisible(False)  # 初始隐藏
        
        status_layout = QVBoxLayout(self.status_frame)
        status_layout.setContentsMargins(15, 15, 15, 15)
        status_layout.setSpacing(10)
        
        self.status_label = QLabel("准备发送...")
        self.status_label.setStyleSheet("color: #f8f8f2; font-size: 11pt;")
        status_layout.addWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #3c4454;
                border-radius: 4px;
                background-color: #1b1e23;
                text-align: center;
                color: #f8f8f2;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #5e7ce0;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(self.status_frame)
        
        # 发送按钮
        self.send_btn = QPushButton("发送批量消息")
        self.send_btn.setMinimumHeight(45)
        self.send_btn.clicked.connect(self._on_send_clicked)
        self.send_btn.setEnabled(False)  # 初始禁用
        self.send_btn.setStyleSheet("""
            QPushButton {
                background-color: #5e7ce0;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 12pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4a6bc8;
            }
            QPushButton:pressed {
                background-color: #3f5ba9;
            }
            QPushButton:disabled {
                background-color: #3c4454;
                color: #8a8a8a;
            }
        """)
        layout.addWidget(self.send_btn)
        
        # 提示信息
        self.hint_label = QLabel("请先登录WhatsApp并输入消息内容")
        self.hint_label.setAlignment(Qt.AlignCenter)
        self.hint_label.setStyleSheet("color: #a0a0a0; font-size: 10pt; margin-top: 10px;")
        layout.addWidget(self.hint_label)
        
        # 更新按钮状态
        self._update_send_button_state()
    
    def _on_message_changed(self):
        """消息内容变化时的处理"""
        text = self.message_input.toPlainText()
        char_count = len(text)
        
        # 更新字符计数
        if char_count > 1000:
            self.char_count_label.setText(f"{char_count} / 1000 字符 (超出限制)")
            self.char_count_label.setStyleSheet("color: #dc3545; font-size: 10pt;")
            
            # 截断文本
            cursor = self.message_input.textCursor()
            cursor.setPosition(1000)
            cursor.movePosition(QTextCursor.End, QTextCursor.KeepAnchor)
            cursor.removeSelectedText()
        else:
            self.char_count_label.setText(f"{char_count} / 1000 字符")
            self.char_count_label.setStyleSheet("color: #a0a0a0; font-size: 10pt;")
        
        # 更新发送按钮状态
        self._update_send_button_state()
    
    def _update_send_button_state(self):
        """更新发送按钮状态"""
        message_text = self.message_input.toPlainText().strip()
        has_message = len(message_text) > 0
        
        # 按钮启用条件：已登录 AND 有消息内容 AND 不在发送中
        can_send = self.is_logged_in and has_message and not self.is_sending
        
        self.send_btn.setEnabled(can_send)
        
        # 更新提示文字
        if not self.is_logged_in:
            self.hint_label.setText("请先登录WhatsApp")
        elif not has_message:
            self.hint_label.setText("请输入消息内容")
        elif self.is_sending:
            self.hint_label.setText("正在发送中...")
        else:
            self.hint_label.setText("准备就绪，可以发送")
    
    def set_login_status(self, is_logged_in: bool):
        """
        设置登录状态
        
        Args:
            is_logged_in: 是否已登录
        """
        self.is_logged_in = is_logged_in
        self._update_send_button_state()
    
    def set_sending_status(self, is_sending: bool, progress: int = 0, status_text: str = ""):
        """
        设置发送状态
        
        Args:
            is_sending: 是否正在发送
            progress: 发送进度 (0-100)
            status_text: 状态文本
        """
        self.is_sending = is_sending
        
        if is_sending:
            self.status_frame.setVisible(True)
            self.status_label.setText(status_text or "正在发送...")
            self.progress_bar.setValue(progress)
        else:
            self.status_frame.setVisible(False)
        
        self._update_send_button_state()
    
    def _on_send_clicked(self):
        """发送按钮点击处理"""
        message_text = self.message_input.toPlainText().strip()
        
        if not message_text:
            QMessageBox.warning(self, "警告", "请输入消息内容")
            return
        
        if not self.is_logged_in:
            QMessageBox.warning(self, "警告", "请先登录WhatsApp")
            return
        
        # 确认发送
        reply = QMessageBox.question(
            self, 
            "确认发送", 
            f"确定要发送以下消息吗？\n\n{message_text[:100]}{'...' if len(message_text) > 100 else ''}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            interval = self.interval_spinbox.value()
            self.send_message_requested.emit(message_text, interval)
    
    def get_message_text(self) -> str:
        """获取消息文本"""
        return self.message_input.toPlainText().strip()
    
    def clear_message(self):
        """清空消息内容"""
        self.message_input.clear()
    
    def set_message_text(self, text: str):
        """设置消息文本"""
        self.message_input.setPlainText(text)
    
    def get_send_interval(self) -> int:
        """获取发送间隔（秒）"""
        return self.interval_spinbox.value()
    
    def set_send_interval(self, seconds: int):
        """设置发送间隔"""
        self.interval_spinbox.setValue(max(1, min(60, seconds)))
