"""
AI客服控制器模块
负责管理AI客服界面与后端服务的交互
"""
import os
import sys
import json
import time
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any

# 导入Qt组件
from PySide6.QtCore import QTimer, Qt, QObject, Signal
from PySide6.QtWidgets import QMessageBox, QListWidget, QLabel, QPushButton, QVBoxLayout, QWidget, QHBoxLayout
from PySide6.QtGui import QTextCursor, QFont

# 导入自定义UI组件
from gui.core.multi_store_widget import MultiStoreWidget
from gui.core.store_item_widget import StoreItemWidget

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 确保项目根目录在导入路径中
MAIN_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(MAIN_DIR)

# 导入数据库操作
from database.store_operations import StoreOperations
from database.context_operations import ContextOperations

# 导入WhatsApp客服模块
from chat.whatsapp.chat_service import WhatsAppService, register_store_status_callback
# 导入服务管理器
from chat.service_manager import ChatServiceManager
# 导入拼多多服务管理模块
# from chat.pdd.start_pdd_service import PDDServiceManager

# 创建店铺状态异常信号类
class StoreStatusSignals(QObject):
    store_status_exception = Signal(str)  # 店铺名称信号

# 创建全局信号实例
store_status_signals = StoreStatusSignals()

# 全局回调函数
def handle_store_status_exception(store_name):
    """处理店铺状态异常，发送信号到UI线程"""
    logger.warning(f"检测到店铺{store_name}状态异常，发送信号")
    store_status_signals.store_status_exception.emit(store_name)

class AIServiceController:
    """AI客服控制器类"""
    
    def __init__(self, main_window):
        """
        初始化AI客服控制器
        
        Args:
            main_window: 主窗口实例，用于访问UI元素和更新界面
        """
        self.main_window = main_window
        
        # 初始化数据库操作
        self.store_operations = StoreOperations()
        self.context_operations = ContextOperations()
        
        # 初始化服务管理器
        self.service_manager = ChatServiceManager()
        # 注册平台服务
        # 注册时不直接传递用户名，在创建服务实例时会传递
        self.service_manager.register_platform("whatsapp", WhatsAppService)
        
        # 当前选择的店铺名称和平台
        self.current_store_name = None
        self.current_platform = None
        
        # 轮询定时器和标志
        self.status_poll_timer = None
        self.is_polling = False
        
        # 存储店铺项组件的字典，便于快速查找
        self.store_items = {}
        
        # 初始化UI组件
        self._initialize_ui_components()
        
        # 获取并填充店铺列表
        self.load_stores()
        
        # 连接信号
        self.connect_signals()
        
        # 初始化数据库
        self._ensure_database_initialized()
        
        # 更新运行中服务的显示
        self.update_running_services_display()
        
        # 设置界面样式
        self._apply_styles()
        
        # 注册店铺状态异常回调（使用信号-槽机制）
        register_store_status_callback(handle_store_status_exception)
        store_status_signals.store_status_exception.connect(self.show_store_status_popup)
        logger.info("使用信号-槽机制注册店铺状态异常处理")
        
        logger.info("AI客服控制器初始化完成")
    
    def connect_signals(self):
        """连接UI信号到处理函数"""
        try:
            # 平台选择变化
            self.main_window.ui.load_pages.platform_combo.currentIndexChanged.connect(self.on_platform_changed)
            
            # 启动按钮
            self.main_window.ui.load_pages.start_button.clicked.connect(self.start_service)
            
            # 店铺下拉框选择变化
            self.main_window.ui.load_pages.store_combo.currentIndexChanged.connect(self.on_store_changed)
            
            # 设置tooltips提升用户体验
            self.main_window.ui.load_pages.platform_combo.setToolTip("选择要连接的平台类型")
            self.main_window.ui.load_pages.store_combo.setToolTip("选择要管理的店铺")
            self.main_window.ui.load_pages.start_button.setToolTip("启动AI客服服务")
            
            # 连接页面切换信号，确保多店铺管理UI仅在AI客服页面显示
            if hasattr(self.main_window.ui.load_pages, 'pages'):
                self.main_window.ui.load_pages.pages.currentChanged.connect(self.on_page_changed)
                logger.info("页面切换信号已连接")
            
            logger.info("所有UI信号已连接")
        except Exception as e:
            logger.exception(f"连接信号时出错: {str(e)}")
    
    def load_stores(self):
        """加载并显示店铺列表"""
        try:
            # 获取所有店铺
            stores = self.store_operations.get_all_stores()
            logger.info(f"获取到 {len(stores)} 个店铺")
            
            # 店铺列表显示包含在stores_display_frame中
            if hasattr(self.main_window.ui.load_pages, 'stores_display_frame'):
                # 清除现有项
                self._clear_stores_display()
                
                # 获取库店页面的布局
                if hasattr(self.main_window.ui.load_pages, 'stores_layout'):
                    stores_layout = self.main_window.ui.load_pages.stores_layout
                    
                    # 添加标题和说明 - 优化样式
                    title_label = QLabel("您的店铺列表")
                    title_label.setStyleSheet("""
                        color: white;
                        font-size: 18pt;
                        font-weight: bold;
                        margin-bottom: 10px;
                        padding-bottom: 5px;
                        border-bottom: 2px solid #3498DB;
                    """)
                    
                    # 添加标题到布局
                    stores_layout.addWidget(title_label)
                    
                    # 添加说明文字 - 优化样式
                    if len(stores) > 0:
                        desc_label = QLabel("点击“启动服务”按钮启动店铺的AI客服，服务启动后可切换账号")
                        desc_label.setStyleSheet("""
                            color: black;
                            font-size: 11pt;
                            margin-bottom: 25px;
                            padding: 10px;
                            background-color: #ECF0F1;
                            border-radius: 5px;
                            border-left: 4px solid #3498DB;
                        """)
                        stores_layout.addWidget(desc_label)
                    else:
                        desc_label = QLabel("暂无店铺信息，请先添加店铺")
                        desc_label.setStyleSheet("""
                            color: #7F8C8D;
                            font-size: 11pt;
                            margin-bottom: 25px;
                            padding: 10px;
                            background-color: #FADBD8;
                            border-radius: 5px;
                            border-left: 4px solid #E74C3C;
                        """)
                        stores_layout.addWidget(desc_label)
                    
                    # 遍历所有店铺，创建店铺项组件
                    for store in stores:
                        store_name = store.get("plg_shopname", "")
                        if store_name:
                            # 创建StoreItemWidget实例
                            store_item = StoreItemWidget(store)
                            # 连接信号
                            store_item.start_service_signal.connect(self._on_store_start_service)
                            store_item.switch_account_signal.connect(self._on_store_switch_account)
                            # 保存到UI实例字典中，便于后续查找
                            self.store_items[store_name] = store_item
                            # 添加到布局
                            stores_layout.addWidget(store_item)
                    
                    # 添加弹性空间，确保组件靠上显示
                    stores_layout.addStretch()
                    
                    # 更新店铺服务状态
                    self._update_store_items_status()
                    
                    # 为兼容性考虑，仍然填充店铺下拉框
                    self._populate_store_combo(stores)
                    
                    logger.info(f"已加载并显示 {len(stores)} 个店铺")
                else:
                    logger.warning("找不到stores_layout组件")
            else:
                logger.warning("找不到stores_display_frame组件")
                # 如果无法在新UI中显示，则使用旧方式填充下拉框
                self._populate_store_combo(stores)
        
        except Exception as e:
            logger.exception(f"加载店铺列表时出错: {str(e)}")
            
    def _populate_store_combo(self, stores):
        """填充店铺下拉框（兼容旧功能）"""
        # 清除现有项
        self.main_window.ui.load_pages.store_combo.clear()
        
        # 添加店铺到下拉框
        for store in stores:
            store_name = store.get("plg_shopname", "")
            if store_name:
                self.main_window.ui.load_pages.store_combo.addItem(store_name, store)
                
    def _clear_stores_display(self):
        """清除店铺展示区域"""
        try:
            if hasattr(self.main_window.ui.load_pages, 'stores_layout'):
                stores_layout = self.main_window.ui.load_pages.stores_layout
                
                # 清除所有子组件
                while stores_layout.count() > 0:
                    item = stores_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                        
                # 清除存储的店铺项组件
                self.store_items.clear()
        except Exception as e:
            logger.exception(f"清除店铺展示区域时出错: {str(e)}")
            
    def _on_store_start_service(self, store_name, platform):
        """
        处理店铺项的启动/停止服务信号
        
        Args:
            store_name: 店铺名称
            platform: 平台名称
        """
        try:
            logger.info(f"处理店铺项启动/停止服务信号: {store_name} ({platform})")
            
            # 获取对应的店铺项组件
            store_item = self.store_items.get(store_name)
            if not store_item:
                logger.warning(f"找不到店铺项组件: {store_name}")
                return
                
            # 检查当前服务状态
            is_running = store_item.is_running
            
            if is_running:
                # 如果服务正在运行，则停止服务
                success = self.service_manager.stop_service(platform, store_name)
                if success:
                    logger.info(f"已停止服务: {store_name} ({platform})")
                    # 更新服务状态
                    store_item.update_status(False)
                    # 更新服务列表显示
                    self.update_running_services_display()
                else:
                    logger.error(f"停止服务失败: {store_name} ({platform})")
                    QMessageBox.warning(
                        self.main_window,
                        "停止服务失败",
                        f"停止 {store_name} 的 {platform} 服务失败，请检查日志或稍后重试"
                    )
            else:
                # 如果服务未运行，先检查店铺状态
                store_ops = StoreOperations()
                store_info = store_ops.get_store_by_name(store_name)
                
                if store_info and store_info.get('plg_status', 1) == 0:
                    logger.warning(f"店铺{store_name}状态已关闭(plg_status=0)，拒绝启动服务")
                    # 直接在UI线程中显示弹窗
                    self.show_store_status_popup(store_name)
                    return
                
                # 店铺状态正常，启动服务
                success = self.service_manager.start_service(platform, store_name)
                if success:
                    logger.info(f"已启动服务: {store_name} ({platform})")
                    # 更新服务状态
                    store_item.update_status(True)
                    # 更新服务列表显示
                    self.update_running_services_display()
                    # 开始轮询状态
                    self.start_status_polling()
                else:
                    logger.error(f"启动服务失败: {store_name} ({platform})")
                    QMessageBox.warning(
                        self.main_window,
                        "启动服务失败",
                        f"启动 {store_name} 的 {platform} 服务失败，请检查日志或稍后重试"
                    )
        except Exception as e:
            logger.exception(f"处理店铺项启动/停止服务信号时出错: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "操作错误",
                f"处理服务操作时出错: {str(e)}"
            )
            
    def _on_store_switch_account(self, store_name, platform):
        """
        处理店铺项的切换账号信号
        
        Args:
            store_name: 店铺名称
            platform: 平台名称
        """
        try:
            logger.info(f"处理店铺项切换账号信号: {store_name} ({platform})")
            
            # 直接调用切换账号方法
            self.switch_account_by_info(store_name, platform)
        except Exception as e:
            logger.exception(f"处理店铺项切换账号信号时出错: {str(e)}")
            
    def on_platform_changed(self, index):
        """
        处理平台选择变化
        
        Args:
            index: 当前选择的平台索引
        """
        platform = self.main_window.ui.load_pages.platform_combo.currentText()
        logger.info(f"选择平台: {platform}")
        
        # 这里可以根据不同平台加载不同的店铺或进行其他操作
        self.append_status_log(f"已选择平台: {platform}")
    
    def start_service(self):
        """启动AI客服服务和状态轮询"""
        # 获取选择的平台和店铺
        platform = self.main_window.ui.load_pages.platform_combo.currentText()
        store_combo = self.main_window.ui.load_pages.store_combo
        store_name = store_combo.currentText()
        store_data = store_combo.currentData()
        
        if not platform or not store_name:
            self.append_status_log("请选择平台和店铺")
            return
        
        # 设置当前选择的店铺和平台
        self.current_store_name = store_name
        self.current_platform = platform
        
        # 开始轮询状态
        self.start_status_polling()
        
        # 通过服务管理器启动服务
        self.append_status_log(f"正在启动 {store_name} 的 {platform} 服务...")
        
        # 禁用UI元素，防止重复操作
        self.main_window.ui.load_pages.platform_combo.setEnabled(False)
        self.main_window.ui.load_pages.store_combo.setEnabled(False)
        self.main_window.ui.load_pages.start_button.setEnabled(False)
        
        # 获取当前登录用户名
        username = self._get_current_username()
        logger.info(f"启动服务，使用用户名: {username}")
        
        # 尝试启动服务，传递用户名参数
        success = self.service_manager.start_service(platform, store_name, username=username)
        
        if success:
            self.append_status_log(f"已成功启动 {store_name} 的 {platform} 服务")
            # 更新服务列表显示
            self.update_running_services_display()
            # 重置UI状态，使得可以启动其他店铺
            self.reset_ui_state()
        else:
            self.append_status_log(f"启动 {store_name} 的 {platform} 服务失败")
            # 重置UI状态
            self.reset_ui_state()
    
    def start_status_polling(self):
        """开始轮询检查店铺状态"""
        if self.is_polling:
            return
        
        self.is_polling = True
        self.append_status_log(f"开始监控店铺 {self.current_store_name} 的状态")
        
        # 首次立即检查
        self.check_store_status()
        
        # 设置定时器每20秒检查一次
        self.status_poll_timer = QTimer()
        self.status_poll_timer.timeout.connect(self.check_store_status)
        self.status_poll_timer.start(20000)  # 20秒
    
    def stop_status_polling(self):
        """停止轮询检查店铺状态"""
        if not self.is_polling:
            return
        
        if self.status_poll_timer:
            self.status_poll_timer.stop()
            self.status_poll_timer = None
        
        self.is_polling = False
        self.append_status_log("已停止监控店铺状态")
    
    def check_store_status(self):
        """检查所有运行中的店铺状态"""
        try:
            # 获取所有运行中的服务
            running_services = self.store_operations.get_running_services()
            logger.info(f"当前有 {len(running_services)} 个运行中的服务")
            
            # 更新UI显示
            self.update_running_services_display()
            
            # 检查每个服务的状态并更新日志
            for service in running_services:
                store_name = service.get("store_name", "")
                platform = service.get("platform", "")
                start_time = service.get("start_time", "")
                
                # 这里可以添加更多状态检查逻辑
                self.append_status_log(f"服务正在运行: {store_name} ({platform}) - 启动于 {start_time}")
                
            # 如果没有运行中的服务，也更新状态
            if not running_services:
                self.append_status_log("当前没有运行中的服务")
            
        except Exception as e:
            logger.exception(f"检查店铺状态时出错: {str(e)}")
    
    def stop_selected_service(self):
        """停止选中的服务"""
        try:
            # 确认是否有选中的服务
            if hasattr(self.main_window.ui.load_pages, 'multi_store_widget'):
                # 使用MultiStoreWidget的内置方法处理停止服务操作
                self.main_window.ui.load_pages.multi_store_widget.on_stop_clicked()
            else:
                self.append_status_log("多店铺服务UI组件不存在，无法停止服务")
                logger.warning("多店铺服务UI组件不存在，无法停止服务")
        except Exception as e:
            self.append_status_log(f"停止服务时出错: {str(e)}")
            logger.exception(f"停止服务时出错: {str(e)}")
    
    def update_running_services_display(self):
        """更新运行中服务的显示"""
        try:
            # 获取运行中的服务
            running_services = self.store_operations.get_running_services()
            logger.info(f"获取到 {len(running_services)} 个运行中的服务")
            
            # 只更新店铺项状态，不再显示服务状态框
            self._update_store_items_status(running_services)
            logger.info(f"已更新店铺项状态，共 {len(running_services)} 个运行中的服务")
        except Exception as e:
            logger.exception(f"更新运行中服务显示时出错: {str(e)}")
            
    def _update_store_items_status(self, running_services=None):
        """
        更新店铺项的状态
        
        Args:
            running_services: 运行中的服务列表，如果为None则自动获取
        """
        try:
            # 如果没有提供运行中的服务列表，则自动获取
            if running_services is None:
                running_services = self.store_operations.get_running_services()
                
            # 创建运行中服务的字典，便于快速查找
            running_dict = {}
            for service in running_services:
                store_name = service.get("store_name", "")
                platform = service.get("platform", "")
                if store_name and platform:
                    running_dict[(store_name, platform)] = True
                    
            # 遍历所有店铺项，更新状态
            for store_name, store_item in self.store_items.items():
                platform = store_item.get_platform()
                is_running = (store_name, platform) in running_dict
                store_item.update_status(is_running)
                
            logger.info(f"已更新 {len(self.store_items)} 个店铺项的状态")
        except Exception as e:
            logger.exception(f"更新店铺项状态时出错: {str(e)}")
    
    def append_status_log(self, message):
        """
        向日志系统添加日志，并显示在UI中
        
        Args:
            message: 日志消息
        """
        # 记录到后台日志
        logger.info(message)
        
    def show_store_status_popup(self, store_name):
        """
        显示店铺状态异常弹窗
        
        Args:
            store_name: 店铺名称
        """
        error_message = "店铺已关闭或需要在平台上重新启动或可能您已经欠费了请在平台上充值，请联系管理员。"
        logger.warning(f"显示店铺{store_name}状态异常弹窗")
        QMessageBox.warning(self.main_window, "店铺状态异常", error_message)
        
        try:
            # 获取时间戳
            timestamp = time.strftime("%H:%M:%S", time.localtime())
            formatted_message = f"[{timestamp}] {message}"
            
            # 将日志显示在状态日志文本框中
            if hasattr(self.main_window.ui.load_pages, 'status_log'):
                status_log = self.main_window.ui.load_pages.status_log
                
                # 移动光标到文本框的最后
                cursor = status_log.textCursor()
                cursor.movePosition(QTextCursor.End)
                status_log.setTextCursor(cursor)
                
                # 添加日志消息
                status_log.insertHtml(f"<p style='margin: 3px 0;'>{formatted_message}</p>")
                
                # 滚动到最新的消息
                status_log.ensureCursorVisible()
        except Exception as e:
            logger.exception(f"在UI中显示状态日志时出错: {str(e)}")
    
    def _do_append_status_log(self, message):
        """
        实际执行日志添加操作，现在只记录到日志系统
        
        Args:
            message: 日志消息
        """
        # 此方法不再执行任何UI操作，仅作为兼容保留
        logger.info(message)
    
    def reset_ui_state(self):
        """重置UI状态"""
        # 确保在主线程中更新UI
        if threading.current_thread() is threading.main_thread():
            self._do_reset_ui_state()
        else:
            # 使用Qt信号槽机制在其他线程中安全更新UI
            QTimer.singleShot(0, self._do_reset_ui_state)
    
    def on_store_changed(self, index):
        """
        处理店铺选择变化
        
        Args:
            index: 当前选择的店铺索引
        """
        if index < 0:
            return
    
    def _do_reset_ui_state(self):
        """实际执行UI状态重置"""
        if not hasattr(self, 'main_window') or not hasattr(self.main_window, 'ui') or not hasattr(self.main_window.ui, 'load_pages'):
            logger.error("UI元素不存在，无法重置状态")
            return
            
        self.main_window.ui.load_pages.platform_combo.setEnabled(True)
        self.main_window.ui.load_pages.store_combo.setEnabled(True)
        self.main_window.ui.load_pages.start_button.setEnabled(True)
        self.main_window.ui.load_pages.start_button.setText("启动服务")
        
        # 更新运行中服务的显示
        self.update_running_services_display()
        
    def _ensure_database_initialized(self):
        """确保数据库表已初始化"""
        try:
            from database.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            success = db_manager.init_database()
            if success:
                logger.info("数据库表初始化成功")
            else:
                logger.error("数据库表初始化失败")
                self.append_status_log("数据库初始化失败，请检查日志")
        except Exception as e:
            logger.exception(f"初始化数据库时出错: {str(e)}")
            
    def _apply_styles(self):
        """应用美化样式到UI元素"""
        try:
            # 美化平台下拉框
            self.main_window.ui.load_pages.platform_combo.setStyleSheet(
                "QComboBox {border: 1px solid #bbb; border-radius: 3px; padding: 1px 18px 1px 3px; min-height: 30px;}"
                "QComboBox::drop-down {subcontrol-origin: padding; subcontrol-position: top right; width: 15px;}"
                "QComboBox QAbstractItemView {border: 1px solid #bbb; selection-background-color: #e0f2f1;}"
            )
            
            # 美化店铺下拉框
            self.main_window.ui.load_pages.store_combo.setStyleSheet(
                "QComboBox {border: 1px solid #bbb; border-radius: 3px; padding: 1px 18px 1px 3px; min-height: 30px;}"
                "QComboBox::drop-down {subcontrol-origin: padding; subcontrol-position: top right; width: 15px;}"
                "QComboBox QAbstractItemView {border: 1px solid #bbb; selection-background-color: #e0f2f1;}"
            )
            
            # 美化启动按钮
            self.main_window.ui.load_pages.start_button.setStyleSheet(
                "QPushButton {background-color: #4CAF50; color: white; border-radius: 4px; padding: 8px; min-height: 30px; font-weight: bold;}"
                "QPushButton:hover {background-color: #45a049;}"
                "QPushButton:pressed {background-color: #3d8b40;}"
                "QPushButton:disabled {background-color: #cccccc; color: #666666;}"
            )
            
            logger.info("已应用UI美化样式")
        except Exception as e:
            logger.exception(f"应用样式时出错: {str(e)}")
            
    def on_store_changed(self, index):
        """
        处理店铺选择变化
        
        Args:
            index: 当前选择的店铺索引
        """
        if index < 0:
            return
            
        # 获取当前选择的店铺名称
        self.current_store_name = self.main_window.ui.load_pages.store_combo.currentText()
        logger.info(f"已选择店铺: {self.current_store_name}")
        
    def on_page_changed(self, index):
        """
        处理页面切换事件
        
        Args:
            index: 当前页面索引
        """
        try:
            # 获取当前页面
            if not hasattr(self.main_window.ui.load_pages, 'pages'):
                return
                
            current_page = self.main_window.ui.load_pages.pages.widget(index)
            if not current_page or not hasattr(current_page, 'objectName'):
                return
                
            # 获取当前页面名称
            page_name = current_page.objectName().lower()
            logger.info(f"切换到页面: {page_name}")
            
            # 判断是否为AI客服页面
            is_ai_page = (page_name == 'page_5' or 
                     'ai_service' in page_name or 'aiservice' in page_name or 
                     'page_ai' in page_name)
            
            # 如果是AI客服页面，更新店铺状态
            if is_ai_page:
                self.update_running_services_display()
                logger.info("更新AI客服页面店铺状态")
                
        except Exception as e:
            logger.exception(f"处理页面切换时出错: {str(e)}")

    def stop_service_by_info(self, store_name, platform):
        """根据店铺名称和平台停止服务
    
        Args:
            store_name: 店铺名称
            platform: 平台名称
        """
        try:
            logger.info(f"准备停止服务: {store_name} ({platform})")
            
            # 调用服务管理器停止服务
            success = self.service_manager.stop_service(platform, store_name)
            
            if success:
                logger.info(f"已停止服务: {store_name} ({platform})")
                # 更新服务列表
                self.update_running_services_display()
            else:
                logger.error(f"停止服务失败: {store_name} ({platform})")
                
        except Exception as e:
            logger.exception(f"停止服务时出错: {str(e)}")
            
    def switch_account_by_info(self, store_name, platform):
        """根据店铺名称和平台切换账号
        
        Args:
            store_name: 店铺名称
            platform: 平台名称
        """
        try:
            # 将参数platform保存为platform_name，防止与模块冲突
            platform_name = platform
            
            logger.info(f"准备切换账号: {store_name} ({platform_name})")
            
            # 先停止服务
            stop_success = self.service_manager.stop_service(platform_name, store_name)
            if not stop_success:
                logger.error(f"切换账号前停止服务失败: {store_name} ({platform_name})")
                return
                
            # 获取服务实例
            service = self.service_manager.get_service(platform_name, store_name)
            
            # 如果是WhatsApp平台，执行切换账号操作
            if platform_name.lower() == 'whatsapp':
                logger.info(f"清除WhatsApp会话数据和cookies: {store_name}")
                
                import shutil
                import os
                import glob
                from pathlib import Path
                
                # 获取安全的店铺名称，使用用户名+店铺名作为唯一索引
                def get_safe_store_name(store_name, username=None):
                    # 获取当前用户名，如果没有提供，从会话文件或其他渠道获取
                    if not username:
                        try:
                            # 从会话文件获取用户信息 - 这是最可靠的方法
                            import json
                            session_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                                      'login_window', 'session.json')
                            
                            if os.path.exists(session_file):
                                try:
                                    with open(session_file, 'r', encoding='utf-8') as f:
                                        session_data = json.load(f)
                                        if 'username' in session_data:
                                            username = session_data['username']
                                            logger.info(f"从会话文件获取到用户名: {username}")
                                except Exception as e:
                                    logger.warning(f"读取会话文件失败: {str(e)}")
                            
                            # 如果还是没有找到，尝试从其他渠道获取
                            if not username and hasattr(self.main_window, 'current_user') and self.main_window.current_user:
                                username = self.main_window.current_user.get('username')
                                logger.info(f"从主窗口获取到用户名: {username}")
                            
                            # 如果仍然没有找到，尝试从登录窗口获取
                            if not username and hasattr(self.main_window, 'login_window') and \
                               hasattr(self.main_window.login_window, 'current_user') and \
                               self.main_window.login_window.current_user:
                                username = self.main_window.login_window.current_user.get('username')
                                logger.info(f"从登录窗口获取到用户名: {username}")
                            
                            # 如果仍然没有用户名，使用默认值
                            if not username:
                                username = 'default_user'
                                logger.warning("无法获取用户名，使用默认值")
                        except Exception as e:
                            logger.warning(f"获取当前用户名时出错: {str(e)}")
                            username = 'default_user'
                    
                    # 组合用户名和店铺名
                    combined_name = f"{username}_{store_name}"
                    logger.info(f"创建唯一存储目录: 用户名={username}, 店铺名={store_name}")
                    
                    # 移除非法文件名字符
                    safe_name = "".join([c for c in combined_name if c.isalnum() or c in "_-"])
                    if not safe_name:
                        safe_name = f"store_{hash(combined_name) % 10000}"
                    
                    logger.info(f"生成的安全存储名称: {safe_name}")
                    return safe_name
                
                # 使用用户名+店铺名生成唯一的存储目录名
                safe_store_name = get_safe_store_name(store_name)
                
                # 获取项目根目录，确保使用绝对路径
                current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                logger.info(f"项目根目录: {current_dir}")
                
                # 使用绝对路径
                whatsapp_dir = os.path.join(current_dir, 'chat', 'whatsapp')
                logger.info(f"WhatsApp目录: {whatsapp_dir}")
                
                # 确保目录存在
                if not os.path.exists(whatsapp_dir):
                    logger.error(f"WhatsApp目录不存在: {whatsapp_dir}")
                    QMessageBox.critical(
                        self.main_window,
                        "目录错误",
                        f"WhatsApp目录不存在: {whatsapp_dir}\n无法切换账号"
                    )
                    return
                    
                node_modules_path = os.path.join(whatsapp_dir, 'node_modules')
                
                # 使用Path对象处理路径，便于跨平台兼容
                from pathlib import Path
                whatsapp_path = Path(whatsapp_dir)
                
                logger.info(f"开始清除WhatsApp账户{store_name}的缓存文件...")
                
                # 0. 强制结束可能还在运行的浏览器进程
                try:
                    import subprocess
                    import sys_platform
                    from platform import system as get_system_name
                    logger.info("尝试终止可能还在运行的Chrome/Puppeteer进程...")
                    if get_system_name() == "Windows":
                        subprocess.run(["taskkill", "/F", "/IM", "chrome.exe"], 
                                        stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                        subprocess.run(["taskkill", "/F", "/IM", "node.exe"], 
                                        stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    else:  # Linux/Mac
                        subprocess.run(["pkill", "-f", "chrome"], 
                                        stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                        subprocess.run(["pkill", "-f", "node"], 
                                        stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    logger.info("已尝试终止相关浏览器进程")
                except Exception as e:
                    logger.warning(f"终止浏览器进程时出错: {str(e)}")
                    
                # 使用已经在函数开始处定义的platform_name变量
                
                # 1. 清除店铺的store_data目录
                try:
                    store_data_dir = whatsapp_path / "store_data" / safe_store_name
                    if store_data_dir.exists():
                        # 1.1 首先清除最重要的session子目录（WhatsApp Web.js实际存储会话的地方）
                        session_dir = store_data_dir / "session"
                        if session_dir.exists():
                            try:
                                shutil.rmtree(session_dir)
                                logger.info(f"成功清除关键会话目录: {session_dir}")
                                # 重新创建空目录
                                session_dir.mkdir(exist_ok=True, parents=True)
                                logger.info(f"已重新创建空的会话目录: {session_dir}")
                            except Exception as e:
                                logger.error(f"清除会话目录失败: {session_dir}, 错误: {str(e)}")
                                QMessageBox.critical(
                                    self.main_window,
                                    "清除关键缓存失败",
                                    f"无法清除WhatsApp会话目录，账号切换可能失败。\n请先关闭所有Chrome浏览器窗口。"
                                )
                                return False
                        
                        # 1.2 清除其他数据文件
                        file_count = 0
                        for file_pattern in ['*.json', '*.session', '*.cookies', '*.dat']:
                            for file_path in store_data_dir.glob(file_pattern):
                                try:
                                    file_path.unlink()
                                    logger.info(f"已删除数据文件: {file_path}")
                                    file_count += 1
                                except Exception as e:
                                    logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
                        
                        logger.info(f"已清除 {file_count} 个文件从店铺数据目录: {store_data_dir}")
                        
                        # 创建空的消息通信文件
                        ipc_file = store_data_dir / "whatsapp_messages.json"
                        with open(ipc_file, "w", encoding="utf-8") as f:
                            f.write("[]")
                            
                        reply_file = store_data_dir / "whatsapp_replies.json"
                        with open(reply_file, "w", encoding="utf-8") as f:
                            f.write("[]")
                    else:
                        logger.info(f"店铺数据目录不存在: {store_data_dir}")
                        # 创建全部必要目录
                        store_data_dir.mkdir(exist_ok=True, parents=True)
                        (store_data_dir / "session").mkdir(exist_ok=True, parents=True)
                        logger.info(f"已创建店铺数据目录及会话目录: {store_data_dir}")
                        
                        # 创建空的通信文件
                        ipc_file = store_data_dir / "whatsapp_messages.json"
                        with open(ipc_file, "w", encoding="utf-8") as f:
                            f.write("[]")
                            
                        reply_file = store_data_dir / "whatsapp_replies.json"
                        with open(reply_file, "w", encoding="utf-8") as f:
                            f.write("[]")
                except Exception as e:
                    logger.error(f"清除店铺数据目录时出错: {str(e)}")
                    return False
                
                # 2. 清除全局缓存目录（这些影响所有账号）
                dirs_to_clear = [
                    # 不清除 session 目录，因为WhatsApp实际将会话存在store_data/{safe_store_name}/session中
                    # {"path": whatsapp_path / "session" / store_name, "name": "会话目录", "recreate": True},
                    {"path": whatsapp_path / ".wwebjs_cache", "name": "Web.js缓存目录", "recreate": False},
                    {"path": whatsapp_path / ".wwebjs_auth", "name": "Web.js认证目录", "recreate": False}
                ]
                
                success_count = 0
                for dir_info in dirs_to_clear:
                    dir_path = dir_info["path"]
                    dir_name = dir_info["name"]
                    recreate = dir_info["recreate"]
                    
                    if not dir_path.exists():
                        logger.info(f"{dir_name} ({dir_path}) 不存在，无需清除")
                        success_count += 1  # 算作成功，因为不存在就不需要清除
                        continue
                    
                    try:
                        # 尝试删除整个目录
                        shutil.rmtree(dir_path, ignore_errors=False)
                        logger.info(f"成功清除 {dir_name} ({dir_path})")
                        success_count += 1
                        
                        # 如果需要，重新创建空目录
                        if recreate: 
                            dir_path.mkdir(exist_ok=True, parents=True)
                            logger.info(f"已重新创建空的 {dir_name}")
                        
                    except PermissionError:
                        logger.error(f"无法删除 {dir_name} ({dir_path})，权限被拒绝。可能文件正在被使用")
                        QMessageBox.warning(
                            self.main_window,
                            "清除缓存警告",
                            f"无法删除{dir_name}，可能文件正在被使用。\n"
                            f"请关闭所有Chrome浏览器后再尝试。"
                        )
                        return False  # 关键目录清除失败，返回失败
                    except Exception as e:
                        logger.error(f"清除 {dir_name} ({dir_path}) 时出错: {str(e)}")
                
                # 总结清除结果
                if success_count == len(dirs_to_clear):
                    logger.info("WhatsApp账户信息缓存文件清除完成")
                elif success_count > 0:
                    logger.info(f"部分WhatsApp账户信息缓存文件已清除 ({success_count}/{len(dirs_to_clear)})")
                    # 全局目录只是额外清除，不影响核心功能，可以继续
                
                # 3. 检查Node.js依赖
                node_modules_path = os.path.join(whatsapp_dir, 'node_modules')
                if not os.path.exists(node_modules_path) or not os.path.exists(os.path.join(node_modules_path, 'whatsapp-web.js')):
                    logger.warning(f"WhatsApp服务缺少Node.js依赖: {node_modules_path}")
                    QMessageBox.warning(
                        self.main_window,
                        "WhatsApp依赖缺失",
                        f"WhatsApp服务需要Node.js依赖，请手动安装。\n\n"
                        f"请在{whatsapp_dir}目录下运行 'npm install' 命令。"
                    )
                    return False
                else:
                    logger.info("WhatsApp Node.js依赖已存在，准备重启服务")
                
                # 4. 显示正在启动的提示
                QMessageBox.information(
                    self.main_window,
                    "账号切换中",
                    f"缓存清除完成，正在启动WhatsApp并准备扫码登录。\n\n"
                    f"系统会打开浏览器并请求您扫码登录新账号。"
                )
                
                # 5. 重新启动服务，唤起扫码登录
                # 获取当前登录用户名
                username = self._get_current_username()
                logger.info(f"切换账号重启服务，使用用户名: {username}, 店铺: {store_name}")
                restart_success = self.service_manager.start_service(platform_name, store_name, username=username)
                if restart_success:
                    logger.info(f"已重新启动服务，唤起扫码登录: {store_name} ({platform_name})")
                    # 显示成功提示
                    QMessageBox.information(
                        self.main_window,
                        "账号切换成功",
                        f"WhatsApp服务已成功启动，请在打开的浏览器窗口中扫码登录新账号。"
                    )
                    # 更新服务列表
                    self.update_running_services_display()
                    
                    # 显示提示消息
                    QMessageBox.information(
                        self.main_window,
                        "切换账号",
                        f"账号已清除，服务已重启，请扫描二维码完成新账号登录"
                    )
                else:
                    logger.error(f"重新启动服务失败: {store_name} ({platform})")
                    QMessageBox.warning(
                        self.main_window,
                        "切换账号失败",
                        f"重新启动 {store_name} 的 {platform} 服务失败，请稍后重试"
                    )
            else:
                # 其他平台的处理方式
                logger.info(f"当前平台 {platform} 暂不支持切换账号功能")
                QMessageBox.information(
                    self.main_window,
                    "功能暂不支持",
                    f"当前平台 {platform} 暂不支持切换账号功能"
                )
                
        except Exception as e:
            logger.exception(f"切换账号时出错: {str(e)}")
            QMessageBox.critical(
                self.main_window,
                "切换账号错误",
                f"切换账号时出现错误: {str(e)}"
            )
            
    def _initialize_ui_components(self):
        """初始化所需的UI组件"""
        try:
            # 检查必要的UI组件（移除不存在的组件检查）
            required_components = [
                'platform_combo', 'store_combo', 'start_button'
            ]

            missing_components = []
            for component in required_components:
                if not hasattr(self.main_window.ui.load_pages, component):
                    missing_components.append(component)
                    logger.warning(f"UI组件不存在: {component}")

            if missing_components:
                logger.warning(f"AI客服控制器缺少以下UI组件: {missing_components}")
            else:
                logger.info("AI客服控制器所需的UI组件都存在")

        except Exception as e:
            logger.exception(f"初始化UI组件时出错: {str(e)}")
                    
    def _get_current_username(self):
        """获取当前登录的用户名称
        
        Returns:
            str: 当前登录的用户名称，如果无法获取则返回默认值
        """
        try:
            # 从会话文件获取用户信息 - 这是最可靠的方法
            import json
            session_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                        'login_window', 'session.json')
            
            username = None
            
            if os.path.exists(session_file):
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                        if 'username' in session_data:
                            username = session_data['username']
                            logger.info(f"从会话文件获取到用户名: {username}")
                except Exception as e:
                    logger.warning(f"读取会话文件失败: {str(e)}")
            
            # 如果还是没有找到，尝试从其他渠道获取
            if not username and hasattr(self.main_window, 'current_user') and self.main_window.current_user:
                username = self.main_window.current_user.get('username')
                logger.info(f"从主窗口获取到用户名: {username}")
            
            # 如果仍然没有找到，尝试从登录窗口获取
            if not username and hasattr(self.main_window, 'login_window') and \
               hasattr(self.main_window.login_window, 'current_user') and \
               self.main_window.login_window.current_user:
                username = self.main_window.login_window.current_user.get('username')
                logger.info(f"从登录窗口获取到用户名: {username}")
            
            # 如果仍然没有用户名，使用默认值
            if not username:
                username = 'default_user'
                logger.warning("无法获取用户名，使用默认值")
                
            return username
        except Exception as e:
            logger.warning(f"获取当前用户名时出错: {str(e)}")
            return 'default_user'
    
    def _check_current_page_for_store_widget(self):
        """检查当前页面，保留但不再使用多店铺管理UI"""
        # 此方法保留但已经简化，不再显示多店铺管理UI
        # 只在切换到AI客服页面时更新店铺状态
        try:
            if hasattr(self.main_window.ui.load_pages, 'pages'):
                current_page = self.main_window.ui.load_pages.pages.currentWidget()
                if current_page and hasattr(current_page, 'objectName'):
                    page_name = current_page.objectName().lower()
                    # 检查是否为AI客服页面
                    is_ai_page = (page_name == 'page_5' or 
                                'ai_service' in page_name or 'aiservice' in page_name or 
                                'page_ai' in page_name or 'pageai' in page_name)
                    
                    # 如果是AI客服页面，更新店铺状态
                    if is_ai_page:
                        logger.info("当前页面是AI客服页面，更新店铺状态")
                        self.update_running_services_display()
        except Exception as e:
            logger.exception(f"检查当前页面时出错: {str(e)}")
