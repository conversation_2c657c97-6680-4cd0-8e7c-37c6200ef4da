#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复效果的脚本
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_main_window_fixes():
    """检查主窗口修复"""
    logger.info("=== 检查主窗口修复 ===")
    
    main_py_path = project_root / "PyOneDark_Qt_Widgets_Modern_GUI-master" / "main.py"
    
    try:
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查异常处理
        fixes = [
            ('try:', '异常处理块'),
            ('except Exception as e:', '异常捕获'),
            ('print("正在初始化', '详细初始化日志'),
            ('traceback.print_exc()', '异常堆栈打印'),
            ('QMessageBox.critical', '用户错误提示')
        ]
        
        for fix_item, description in fixes:
            if fix_item in content:
                logger.info(f"✓ {description} 已添加")
            else:
                logger.error(f"✗ {description} 缺失")
        
        return True
        
    except Exception as e:
        logger.error(f"检查主窗口修复时出错: {e}")
        return False

def check_ai_controller_fixes():
    """检查AI控制器修复"""
    logger.info("=== 检查AI控制器修复 ===")
    
    ai_controller_path = project_root / "gui" / "core" / "ai_service_controller.py"
    
    try:
        with open(ai_controller_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了不存在的组件检查
        if "'status_text', 'ai_service_frame'" in content:
            logger.error("✗ 仍然检查不存在的UI组件")
            return False
        else:
            logger.info("✓ 已移除对不存在UI组件的检查")
        
        # 检查是否有改进的日志
        if "missing_components" in content:
            logger.info("✓ 添加了改进的组件检查逻辑")
        else:
            logger.warning("⚠ 缺少改进的组件检查逻辑")
        
        return True
        
    except Exception as e:
        logger.error(f"检查AI控制器修复时出错: {e}")
        return False

def check_bulk_controller_fixes():
    """检查群发控制器修复"""
    logger.info("=== 检查群发控制器修复 ===")
    
    bulk_controller_path = project_root / "PyOneDark_Qt_Widgets_Modern_GUI-master" / "gui" / "core" / "bulk_customer_acquisition_controller.py"
    
    try:
        with open(bulk_controller_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查详细日志
        log_checks = [
            ('print("🔥 添加电话号码按钮被点击！")', '添加号码按钮点击日志'),
            ('print("🔥 WhatsApp登录按钮被点击！")', 'WhatsApp登录按钮点击日志'),
            ('print("🔗 添加号码按钮信号连接成功")', '信号连接成功日志'),
            ('logger.info("开始初始化群发获客控制器...")', '详细初始化日志'),
            ('raise  # 重新抛出异常', '异常重新抛出')
        ]
        
        for check_item, description in log_checks:
            if check_item in content:
                logger.info(f"✓ {description} 已添加")
            else:
                logger.error(f"✗ {description} 缺失")
        
        return True
        
    except Exception as e:
        logger.error(f"检查群发控制器修复时出错: {e}")
        return False

def generate_test_instructions():
    """生成测试说明"""
    logger.info("=== 测试说明 ===")
    
    instructions = [
        "1. 启动应用程序: python main.py",
        "2. 观察控制台输出，查找以下信息:",
        "   - '正在初始化群发获客控制器...'",
        "   - '🔗 添加号码按钮信号连接成功'",
        "   - '🔗 WhatsApp登录按钮信号连接成功'",
        "   - '✓ 群发获客控制器初始化完成'",
        "3. 点击左侧菜单的'群发获客'按钮",
        "4. 尝试点击'添加号码'按钮，应该看到:",
        "   - 控制台输出: '🔥 添加电话号码按钮被点击！'",
        "   - 弹出输入对话框",
        "5. 尝试点击'登录WhatsApp'按钮，应该看到:",
        "   - 控制台输出: '🔥 WhatsApp登录按钮被点击！'",
        "   - 启动登录流程",
        "6. 如果仍然无响应，查看控制台的错误信息"
    ]
    
    for instruction in instructions:
        logger.info(instruction)

def main():
    """主测试函数"""
    logger.info("开始测试最终修复效果...")
    logger.info("=" * 60)
    
    tests = [
        ("主窗口修复检查", check_main_window_fixes),
        ("AI控制器修复检查", check_ai_controller_fixes),
        ("群发控制器修复检查", check_bulk_controller_fixes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                logger.info(f"✓ {test_name} 通过")
            else:
                logger.error(f"✗ {test_name} 失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"修复检查结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有修复都已完成！")
        logger.info("")
        generate_test_instructions()
        return True
    else:
        logger.error("⚠️ 部分修复可能有问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
