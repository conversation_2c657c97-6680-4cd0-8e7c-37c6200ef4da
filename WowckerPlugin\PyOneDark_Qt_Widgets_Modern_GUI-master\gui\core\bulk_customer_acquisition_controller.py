"""
群发获客控制器
负责处理群发获客页面的业务逻辑
"""

import logging
import re
import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QMessageBox, QTableWidgetItem, QPushButton, QLabel, QVBoxLayout, QHBoxLayout, QFrame
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QObject
from PySide6.QtGui import QFont, QPixmap

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# 导入数据库操作和WhatsApp功能
from database.telenumber_operations import TeleNumberOperations
from chat.whatsapp.bulk_login_manager import WhatsAppBulkLoginManager
from chat.whatsapp.bulk_sender import WhatsAppBulkSender

# 导入自定义组件
from gui.widgets.international_phone_input import InternationalPhoneInput
from gui.core.country_code_manager import CountryCodeManager

# 设置日志
logger = logging.getLogger(__name__)

class BulkUISignals(QObject):
    """群发获客UI信号类"""
    login_success = Signal()  # 登录成功信号
    login_error = Signal(str)  # 登录错误信号
    qr_code_received = Signal(str)  # QR码接收信号


class BulkCustomerAcquisitionController:
    """群发获客控制器"""

    def __init__(self, main_window):
        try:
            logger.info("开始初始化群发获客控制器...")

            self.main_window = main_window
            self.username = getattr(main_window, 'current_username', 'default_user')  # 获取当前用户名
            self.store_name = "bulk_messaging"  # 群发专用店铺名
            logger.info(f"用户名: {self.username}, 店铺名: {self.store_name}")

            # 创建UI信号对象
            logger.info("创建UI信号对象...")
            self.ui_signals = BulkUISignals()

            # 数据库操作
            logger.info("初始化数据库操作...")
            self.telenumber_ops = TeleNumberOperations()

            # WhatsApp登录和发送管理
            logger.info("初始化WhatsApp管理器...")
            self.login_manager = WhatsAppBulkLoginManager(self.username, self.store_name)
            self.bulk_sender = WhatsAppBulkSender(self.login_manager)

            # 获取UI组件引用（带错误检查）
            logger.info("初始化UI组件引用...")
            ui_success = self._initialize_ui_components()
            if not ui_success:
                logger.error("UI组件初始化失败，但继续执行...")

            # 创建国际电话号码输入组件（暂时移除，专注于核心功能）
            logger.info("初始化国际电话号码管理器...")
            self.country_manager = CountryCodeManager()

            # 设置输入提示
            if hasattr(self, 'message_input_field') and self.message_input_field:
                self.message_input_field.setPlaceholderText("输入要群发的消息内容...")
                logger.info("设置消息输入框提示文本成功")
            else:
                logger.error("消息输入框不存在，无法设置提示文本")

            # 设置回调函数
            logger.info("设置回调函数...")
            self._setup_callbacks()

            # 连接信号（带错误检查）
            logger.info("连接UI信号...")
            self.connect_signals()

            # 初始化表格
            logger.info("初始化电话号码表格...")
            self.init_table()

            # 加载数据库中的电话号码
            logger.info("加载数据库中的电话号码...")
            self.load_phone_numbers_from_db()

            # 更新UI状态
            logger.info("更新UI状态...")
            self.update_ui_state()

            # 设置定时器定期更新UI状态
            logger.info("设置UI状态更新定时器...")
            self.ui_update_timer = QTimer()
            self.ui_update_timer.timeout.connect(self.update_ui_state_timer)
            self.ui_update_timer.start(5000)  # 每5秒更新一次UI状态（减少频率）

            # 测试按钮连接（调试用）
            logger.info("测试按钮连接...")
            self._test_button_connections()

            logger.info(f"✓ 群发获客控制器初始化完成 - 用户: {self.username}")

        except Exception as e:
            logger.exception(f"✗ 群发获客控制器初始化失败: {str(e)}")
            raise  # 重新抛出异常，让主窗口能够捕获

    def _initialize_ui_components(self):
        """初始化UI组件引用（带错误检查）"""
        try:
            # 检查主要UI结构是否存在
            if not hasattr(self.main_window, 'ui'):
                logger.error("主窗口没有ui属性")
                return False

            if not hasattr(self.main_window.ui, 'load_pages'):
                logger.error("UI没有load_pages属性")
                return False

            load_pages = self.main_window.ui.load_pages

            # 获取UI组件引用（逐个检查）
            ui_components = {
                'message_input_field': '消息输入框',
                'phone_numbers_table': '电话号码表格',
                'bulk_send_btn': '群发按钮',
                'add_phone_btn': '添加电话号码按钮',
                'login_status_indicator': '登录状态指示器',
                'login_status_text': '登录状态文本',
                'login_info_text': '登录信息文本',
                'whatsapp_login_btn': 'WhatsApp登录按钮',
                'whatsapp_logout_btn': 'WhatsApp退出登录按钮',
                'send_status_label': '发送状态标签'
            }

            missing_components = []

            for component_name, description in ui_components.items():
                if hasattr(load_pages, component_name):
                    component = getattr(load_pages, component_name)
                    setattr(self, component_name, component)
                    logger.debug(f"✓ 成功获取{description}: {component_name}")
                else:
                    missing_components.append(f"{description}({component_name})")
                    setattr(self, component_name, None)
                    logger.error(f"✗ 缺失{description}: {component_name}")

            if missing_components:
                logger.error(f"缺失的UI组件: {', '.join(missing_components)}")
                return False
            else:
                logger.info("所有UI组件引用获取成功")
                return True

        except Exception as e:
            logger.exception(f"初始化UI组件时出错: {str(e)}")
            return False

    def add_phone_number_dialog(self):
        """显示添加电话号码对话框"""
        try:
            print("🔥 添加电话号码按钮被点击！")  # 使用print确保能看到
            logger.info("添加电话号码按钮被点击")
            from PySide6.QtWidgets import QInputDialog

            phone, ok = QInputDialog.getText(
                self.main_window,
                '添加电话号码',
                '请输入电话号码（支持国际格式，如：+8613812345678 或 13812345678）:'
            )

            logger.info(f"用户输入: phone='{phone}', ok={ok}")

            if ok and phone.strip():
                # 验证并添加电话号码
                is_valid, result = self.validate_phone_number(phone.strip())
                if not is_valid:
                    logger.warning(f"电话号码格式无效: {phone.strip()}, 错误: {result}")
                    QMessageBox.warning(self.main_window, "格式错误", result)
                    return

                # result现在是一个包含不同格式的字典
                phone_formats = result
                whatsapp_format = phone_formats["whatsapp"]  # 用于发送的格式

                logger.info(f"电话号码格式化结果: {phone_formats}")

                # 添加到数据库（存储WhatsApp格式，用于实际发送）
                record_id = self.telenumber_ops.add_phone_number(whatsapp_format, self.username, self.store_name)
                if record_id == -1:
                    logger.warning(f"电话号码已存在: {whatsapp_format}")
                    QMessageBox.warning(self.main_window, "重复号码", "该电话号码已存在")
                    return

                logger.info(f"电话号码添加成功，记录ID: {record_id}")

                # 更新表格显示
                self.update_table_display()

                # 更新UI状态
                self.update_ui_state()

                # 记录日志
                logger.info(f"添加电话号码到数据库: {phone_formats['display']} -> {whatsapp_format} (ID: {record_id})")
                QMessageBox.information(self.main_window, "添加成功", f"电话号码 {phone_formats['display']} 添加成功")
            else:
                logger.info("用户取消了输入或输入为空")

        except Exception as e:
            logger.exception(f"添加电话号码时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"添加电话号码失败: {str(e)}")

    def _setup_callbacks(self):
        """设置回调函数"""
        # 登录管理器回调（使用线程安全的回调）
        self.login_manager.set_callbacks(
            login_status_callback=self.on_login_status_changed_safe,
            qr_code_callback=self.on_qr_code_received_safe,
            error_callback=self.on_login_error_safe
        )

        # 群发发送器回调
        self.bulk_sender.set_callbacks(
            progress_callback=self.on_send_progress,
            completion_callback=self.on_send_completed,
            error_callback=self.on_send_error
        )

    def connect_signals(self):
        """连接信号到槽函数（带错误检查）"""
        try:
            signal_connections = []

            # 群发按钮信号
            if self.bulk_send_btn and hasattr(self.bulk_send_btn, 'clicked'):
                self.bulk_send_btn.clicked.connect(self.start_bulk_send)
                signal_connections.append("群发按钮 -> start_bulk_send")
                logger.debug("✓ 群发按钮信号连接成功")
            else:
                logger.error("✗ 群发按钮不存在或没有clicked信号")

            # 添加电话号码按钮信号
            if self.add_phone_btn and hasattr(self.add_phone_btn, 'clicked'):
                self.add_phone_btn.clicked.connect(self.add_phone_number_dialog)
                signal_connections.append("添加号码按钮 -> add_phone_number_dialog")
                print("🔗 添加号码按钮信号连接成功")  # 使用print确保能看到
                logger.debug("✓ 添加号码按钮信号连接成功")
            else:
                print("❌ 添加号码按钮不存在或没有clicked信号")  # 使用print确保能看到
                logger.error("✗ 添加号码按钮不存在或没有clicked信号")

            # WhatsApp登录按钮信号
            if self.whatsapp_login_btn and hasattr(self.whatsapp_login_btn, 'clicked'):
                self.whatsapp_login_btn.clicked.connect(self.start_whatsapp_login)
                signal_connections.append("WhatsApp登录按钮 -> start_whatsapp_login")
                print("🔗 WhatsApp登录按钮信号连接成功")  # 使用print确保能看到
                logger.debug("✓ WhatsApp登录按钮信号连接成功")
            else:
                print("❌ WhatsApp登录按钮不存在或没有clicked信号")  # 使用print确保能看到
                logger.error("✗ WhatsApp登录按钮不存在或没有clicked信号")

            # WhatsApp退出登录按钮信号
            if self.whatsapp_logout_btn and hasattr(self.whatsapp_logout_btn, 'clicked'):
                self.whatsapp_logout_btn.clicked.connect(self.logout_whatsapp)
                signal_connections.append("WhatsApp退出登录按钮 -> logout_whatsapp")
                logger.debug("✓ WhatsApp退出登录按钮信号连接成功")
            else:
                logger.error("✗ WhatsApp退出登录按钮不存在或没有clicked信号")

            # 表格右键菜单（删除号码）
            if self.phone_numbers_table:
                self.phone_numbers_table.setContextMenuPolicy(Qt.CustomContextMenu)
                self.phone_numbers_table.customContextMenuRequested.connect(self.show_table_context_menu)
                signal_connections.append("电话号码表格 -> show_table_context_menu")
                logger.debug("✓ 电话号码表格右键菜单信号连接成功")
            else:
                logger.error("✗ 电话号码表格不存在")

            # 消息输入框变化时更新UI状态
            if self.message_input_field and hasattr(self.message_input_field, 'textChanged'):
                self.message_input_field.textChanged.connect(self.update_ui_state)
                signal_connections.append("消息输入框 -> update_ui_state")
                logger.debug("✓ 消息输入框信号连接成功")
            else:
                logger.error("✗ 消息输入框不存在或没有textChanged信号")

            # 连接UI信号到槽函数
            self.ui_signals.login_success.connect(self.show_login_success_message)
            self.ui_signals.login_error.connect(self.show_login_error_message)
            self.ui_signals.qr_code_received.connect(self.handle_qr_code_received)
            signal_connections.append("UI信号 -> 相应槽函数")

            logger.info(f"信号连接完成，共连接了 {len(signal_connections)} 个信号")
            for connection in signal_connections:
                logger.debug(f"  - {connection}")

        except Exception as e:
            logger.exception(f"连接信号时出错: {str(e)}")

    def _test_button_connections(self):
        """测试按钮连接是否正常工作"""
        try:
            logger.info("开始测试按钮连接...")

            # 测试添加号码按钮
            if self.add_phone_btn:
                logger.info(f"添加号码按钮类型: {type(self.add_phone_btn)}")
                logger.info(f"添加号码按钮是否启用: {self.add_phone_btn.isEnabled()}")
                logger.info(f"添加号码按钮是否可见: {self.add_phone_btn.isVisible()}")

            # 测试WhatsApp登录按钮
            if self.whatsapp_login_btn:
                logger.info(f"WhatsApp登录按钮类型: {type(self.whatsapp_login_btn)}")
                logger.info(f"WhatsApp登录按钮是否启用: {self.whatsapp_login_btn.isEnabled()}")
                logger.info(f"WhatsApp登录按钮是否可见: {self.whatsapp_login_btn.isVisible()}")

            # 测试群发按钮
            if self.bulk_send_btn:
                logger.info(f"群发按钮类型: {type(self.bulk_send_btn)}")
                logger.info(f"群发按钮是否启用: {self.bulk_send_btn.isEnabled()}")
                logger.info(f"群发按钮是否可见: {self.bulk_send_btn.isVisible()}")

        except Exception as e:
            logger.exception(f"测试按钮连接时出错: {str(e)}")
    
    def init_table(self):
        """初始化电话号码表格"""
        self.phone_numbers_table.setRowCount(0)
        # 设置表格列标题（增强版）
        self.phone_numbers_table.setHorizontalHeaderLabels(["序号", "国家/地区", "电话号码", "添加时间", "状态"])

        # 设置列宽
        header = self.phone_numbers_table.horizontalHeader()
        header.setStretchLastSection(True)  # 最后一列自动拉伸
        self.phone_numbers_table.setColumnWidth(0, 60)   # 序号
        self.phone_numbers_table.setColumnWidth(1, 100)  # 国家/地区
        self.phone_numbers_table.setColumnWidth(2, 150)  # 电话号码
        self.phone_numbers_table.setColumnWidth(3, 120)  # 添加时间

        self.update_table_display()

    def load_phone_numbers_from_db(self):
        """从数据库加载电话号码"""
        try:
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
            logger.info(f"从数据库加载了 {len(phone_records)} 个电话号码")
            self.update_table_display()
        except Exception as e:
            logger.error(f"从数据库加载电话号码时出错: {str(e)}")

    def update_ui_state(self):
        """更新UI状态"""
        try:
            # 检查登录状态
            is_logged_in = self.login_manager.check_login_status()
            is_logging_in = self.login_manager.is_login_in_progress()

            # 检查数据状态
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
            has_numbers = len(phone_records) > 0
            has_message = len(self.message_input_field.toPlainText().strip()) > 0

            # 检查发送状态
            is_sending = self.bulk_sender.is_sending_in_progress()

            # 调试信息
            logger.debug(f"UI状态更新: 登录={is_logged_in}, 登录中={is_logging_in}, 号码数={len(phone_records)}, 有消息={has_message}, 发送中={is_sending}")

            # 更新登录相关按钮
            self.whatsapp_login_btn.setEnabled(not is_logged_in and not is_logging_in)
            self.whatsapp_logout_btn.setEnabled(is_logged_in)

            # 更新登录状态显示
            if is_logged_in:
                self.login_status_indicator.setStyleSheet("color: #4CAF50; font-size: 16pt;")  # 绿色
                self.login_status_text.setText("已登录")
                self.login_info_text.setText("WhatsApp已登录，可以开始群发消息")
                self.login_info_text.setStyleSheet("color: #4CAF50; font-size: 10pt; font-style: italic;")
            elif is_logging_in:
                self.login_status_indicator.setStyleSheet("color: #ffa726; font-size: 16pt;")  # 橙色
                self.login_status_text.setText("登录中...")
                self.login_info_text.setText("正在登录WhatsApp，请扫描二维码")
                self.login_info_text.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")
            else:
                self.login_status_indicator.setStyleSheet("color: #ff4444; font-size: 16pt;")  # 红色
                self.login_status_text.setText("未登录")
                self.login_info_text.setText("请先登录WhatsApp才能使用群发功能")
                self.login_info_text.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")

            # 群发按钮启用条件（关键要求：只有登录成功且有消息内容时才可点击）
            can_send = is_logged_in and has_numbers and has_message and not is_sending
            self.bulk_send_btn.setEnabled(can_send)

            # 更新发送状态提示
            if not is_logged_in:
                self.send_status_label.setText("请先登录WhatsApp")
                self.send_status_label.setStyleSheet("color: #ff4444; font-size: 10pt; font-style: italic;")
            elif not has_message:
                self.send_status_label.setText("请输入消息内容")
                self.send_status_label.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")
            elif not has_numbers:
                self.send_status_label.setText("请先添加电话号码")
                self.send_status_label.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")
            elif is_sending:
                self.send_status_label.setText("正在发送中...")
                self.send_status_label.setStyleSheet("color: #2196F3; font-size: 10pt; font-style: italic;")
            else:
                self.send_status_label.setText("准备就绪，可以群发")
                self.send_status_label.setStyleSheet("color: #4CAF50; font-size: 10pt; font-style: italic;")

            # 调试群发按钮状态
            if not can_send:
                reasons = []
                if not is_logged_in:
                    reasons.append("未登录")
                if not has_numbers:
                    reasons.append("无电话号码")
                if not has_message:
                    reasons.append("无消息内容")
                if is_sending:
                    reasons.append("正在发送中")
                logger.debug(f"群发按钮禁用原因: {', '.join(reasons)}")

            # 更新按钮文本
            if is_sending:
                self.bulk_send_btn.setText("发送中...")
            else:
                self.bulk_send_btn.setText("群发消息")

        except Exception as e:
            logger.error(f"更新UI状态时出错: {str(e)}")

    def update_ui_state_timer(self):
        """定时器触发的UI状态更新（减少日志输出）"""
        try:
            # 检查登录状态
            is_logged_in = self.login_manager.check_login_status()
            is_logging_in = self.login_manager.is_login_in_progress()

            # 检查数据状态
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
            has_numbers = len(phone_records) > 0
            has_message = len(self.message_input_field.toPlainText().strip()) > 0

            # 检查发送状态
            is_sending = self.bulk_sender.is_sending_in_progress()

            # 更新登录相关按钮
            self.whatsapp_login_btn.setEnabled(not is_logged_in and not is_logging_in)
            self.whatsapp_logout_btn.setEnabled(is_logged_in)

            # 更新登录状态显示
            if is_logged_in:
                self.login_status_indicator.setStyleSheet("color: #4CAF50; font-size: 16pt;")  # 绿色
                self.login_status_text.setText("已登录")
                if not hasattr(self, '_login_info_updated') or not self._login_info_updated:
                    self.login_info_text.setText("WhatsApp已登录，可以开始群发消息")
                    self.login_info_text.setStyleSheet("color: #4CAF50; font-size: 10pt; font-style: italic;")
            elif is_logging_in:
                self.login_status_indicator.setStyleSheet("color: #ffa726; font-size: 16pt;")  # 橙色
                self.login_status_text.setText("登录中...")
                self.login_info_text.setText("正在登录WhatsApp，请扫描二维码")
                self.login_info_text.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")
            else:
                self.login_status_indicator.setStyleSheet("color: #ff4444; font-size: 16pt;")  # 红色
                self.login_status_text.setText("未登录")
                self.login_info_text.setText("请先登录WhatsApp才能使用群发功能")
                self.login_info_text.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")

            # 群发按钮启用条件
            can_send = is_logged_in and has_numbers and has_message and not is_sending
            self.bulk_send_btn.setEnabled(can_send)

            # 更新按钮文本
            if is_sending:
                self.bulk_send_btn.setText("发送中...")
            else:
                self.bulk_send_btn.setText("群发消息")

        except Exception as e:
            logger.error(f"定时器更新UI状态时出错: {str(e)}")

    def validate_phone_number(self, phone):
        """验证电话号码格式并转换为WhatsApp格式"""
        # 移除所有空格和特殊字符，只保留数字和+号
        cleaned_phone = re.sub(r'[^\d+]', '', phone.strip())

        # 检查是否为空
        if not cleaned_phone:
            return False, "电话号码不能为空"

        # 移除+号，统一处理
        if cleaned_phone.startswith('+'):
            cleaned_phone = cleaned_phone[1:]

        # 验证和格式化不同类型的号码
        if len(cleaned_phone) == 11 and cleaned_phone.startswith(('13', '14', '15', '16', '17', '18', '19')):
            # 中国手机号码（11位）
            whatsapp_format = f"86{cleaned_phone}@c.us"
            display_format = f"+86 {cleaned_phone[:3]} {cleaned_phone[3:7]} {cleaned_phone[7:]}"
            return True, {"whatsapp": whatsapp_format, "display": display_format, "original": cleaned_phone}

        elif len(cleaned_phone) >= 10 and len(cleaned_phone) <= 15:
            # 国际号码
            if cleaned_phone.startswith('86') and len(cleaned_phone) == 13:
                # 已经包含86国家代码的中国号码
                mobile_part = cleaned_phone[2:]
                if mobile_part.startswith(('13', '14', '15', '16', '17', '18', '19')):
                    whatsapp_format = f"{cleaned_phone}@c.us"
                    display_format = f"+86 {mobile_part[:3]} {mobile_part[3:7]} {mobile_part[7:]}"
                    return True, {"whatsapp": whatsapp_format, "display": display_format, "original": cleaned_phone}

            # 其他国际号码
            whatsapp_format = f"{cleaned_phone}@c.us"
            display_format = f"+{cleaned_phone}"
            return True, {"whatsapp": whatsapp_format, "display": display_format, "original": cleaned_phone}

        else:
            return False, "电话号码格式不正确，请输入有效的手机号码"

    def format_phone_for_display(self, whatsapp_format):
        """将WhatsApp格式的电话号码转换为用户友好的显示格式"""
        try:
            # 移除@c.us后缀
            if whatsapp_format.endswith('@c.us'):
                phone_number = whatsapp_format[:-5]
            else:
                phone_number = whatsapp_format

            # 处理中国号码
            if phone_number.startswith('86') and len(phone_number) == 13:
                mobile_part = phone_number[2:]
                if mobile_part.startswith(('13', '14', '15', '16', '17', '18', '19')):
                    return f"+86 {mobile_part[:3]} {mobile_part[3:7]} {mobile_part[7:]}"

            # 处理其他国际号码
            if len(phone_number) >= 10:
                return f"+{phone_number}"

            # 如果无法识别格式，返回原始格式
            return whatsapp_format

        except Exception as e:
            logger.error(f"格式化电话号码显示时出错: {str(e)}")
            return whatsapp_format


    
    def update_table_display(self):
        """更新表格显示（增强版）"""
        try:
            # 从数据库获取电话号码
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)

            self.phone_numbers_table.setRowCount(len(phone_records))

            for i, record in enumerate(phone_records):
                # 序号
                item_index = QTableWidgetItem(str(i + 1))
                item_index.setTextAlignment(Qt.AlignCenter)
                # 存储记录ID用于删除操作
                item_index.setData(Qt.UserRole, record['id'])
                self.phone_numbers_table.setItem(i, 0, item_index)

                # 解析国家信息
                country_info = self._parse_country_from_phone(record['phone_number'])

                # 国家/地区
                if country_info:
                    country_text = f"{country_info['flag']} {country_info['name_cn']}"
                else:
                    country_text = "🌍 未知"
                item_country = QTableWidgetItem(country_text)
                item_country.setTextAlignment(Qt.AlignCenter)
                self.phone_numbers_table.setItem(i, 1, item_country)

                # 电话号码（转换为用户友好的显示格式）
                display_phone = self.format_phone_for_display(record['phone_number'])
                item_phone = QTableWidgetItem(display_phone)
                item_phone.setTextAlignment(Qt.AlignCenter)
                # 存储原始的WhatsApp格式用于发送
                item_phone.setData(Qt.UserRole, record['phone_number'])
                self.phone_numbers_table.setItem(i, 2, item_phone)

                # 添加时间
                created_time = record.get('created_at', '')
                if created_time:
                    try:
                        # 尝试格式化时间
                        if isinstance(created_time, str):
                            time_display = created_time[:16]  # 只显示到分钟
                        else:
                            time_display = str(created_time)[:16]
                    except:
                        time_display = "未知"
                else:
                    time_display = "未知"

                item_time = QTableWidgetItem(time_display)
                item_time.setTextAlignment(Qt.AlignCenter)
                self.phone_numbers_table.setItem(i, 3, item_time)

                # 状态（翻译为中文）
                status_map = {
                    'pending': '待发送',
                    'sending': '发送中',
                    'sent': '已发送',
                    'failed': '发送失败',
                    'blocked': '已拉黑'
                }
                status_text = status_map.get(record['status'], record['status'])
                item_status = QTableWidgetItem(status_text)
                item_status.setTextAlignment(Qt.AlignCenter)

                # 根据状态设置颜色
                if record['status'] == 'sent':
                    item_status.setForeground(Qt.green)
                elif record['status'] == 'failed':
                    item_status.setForeground(Qt.red)
                elif record['status'] == 'sending':
                    item_status.setForeground(Qt.yellow)
                elif record['status'] == 'blocked':
                    item_status.setForeground(Qt.darkRed)

                self.phone_numbers_table.setItem(i, 4, item_status)

        except Exception as e:
            logger.error(f"更新表格显示时出错: {str(e)}")

    def _parse_country_from_phone(self, whatsapp_format):
        """从WhatsApp格式的电话号码解析国家信息"""
        try:
            if whatsapp_format.endswith('@c.us'):
                phone_number = whatsapp_format[:-5]
                parsed = self.country_manager.parse_international_number(phone_number)
                if parsed:
                    country_code, _ = parsed
                    return self.country_manager.get_country_info(country_code)
        except Exception as e:
            logger.error(f"解析国家信息时出错: {str(e)}")
        return None
    
    def start_bulk_send(self):
        """开始群发消息"""
        try:
            # 检查登录状态
            if not self.login_manager.check_login_status():
                QMessageBox.warning(self.main_window, "未登录", "请先登录WhatsApp")
                return

            # 获取电话号码列表
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
            if not phone_records:
                QMessageBox.warning(self.main_window, "无号码", "请先添加电话号码")
                return

            # 检查消息内容
            message = self.message_input_field.toPlainText().strip()
            if not message:
                QMessageBox.warning(self.main_window, "无消息", "请输入要发送的消息内容")
                return

            # 提取电话号码（已经是WhatsApp格式）
            phone_numbers = [record['phone_number'] for record in phone_records]

            # 验证所有号码都是WhatsApp格式
            for phone in phone_numbers:
                if not phone.endswith('@c.us'):
                    logger.warning(f"电话号码格式可能不正确: {phone}")

            logger.info(f"准备群发到以下号码: {phone_numbers}")

            # 确认发送
            reply = QMessageBox.question(
                self.main_window,
                "确认群发",
                f"确定要向 {len(phone_numbers)} 个号码发送消息吗？\n\n消息内容：\n{message[:100]}{'...' if len(message) > 100 else ''}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 更新所有号码状态为发送中
            for record in phone_records:
                self.telenumber_ops.update_phone_status(record['id'], 'sending')

            # 更新表格显示
            self.update_table_display()

            # 开始群发
            success = self.bulk_sender.send_bulk_messages(phone_numbers, message, delay=3000)
            if not success:
                # 如果启动失败，恢复状态
                for record in phone_records:
                    self.telenumber_ops.update_phone_status(record['id'], 'pending')
                self.update_table_display()
                return

            # 更新UI状态
            self.update_ui_state()

            logger.info(f"开始群发消息到 {len(phone_numbers)} 个号码")

        except Exception as e:
            logger.exception(f"启动群发时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"启动群发失败: {str(e)}")
    
    # ========== WhatsApp登录相关方法 ==========

    def start_whatsapp_login(self):
        """启动WhatsApp登录"""
        try:
            print("🔥 WhatsApp登录按钮被点击！")  # 使用print确保能看到
            logger.info("WhatsApp登录按钮被点击")
            logger.info(f"当前登录状态: is_logged_in={self.login_manager.is_logged_in}, is_logging_in={self.login_manager.is_logging_in}")

            # 检查是否已经在登录中
            if self.login_manager.is_logging_in:
                logger.warning("已经在登录中，忽略重复点击")
                QMessageBox.information(self.main_window, "登录中", "正在登录中，请等待...")
                return

            # 检查是否已经登录
            if self.login_manager.is_logged_in:
                logger.warning("已经登录，无需重复登录")
                QMessageBox.information(self.main_window, "已登录", "WhatsApp已经登录")
                return

            logger.info("开始启动WhatsApp登录流程")
            success = self.login_manager.start_login()
            logger.info(f"登录启动结果: {success}")

            if success:
                self.update_ui_state()
                QMessageBox.information(self.main_window, "登录中", "正在启动WhatsApp登录，请在弹出的浏览器中扫描二维码")
                logger.info("登录流程启动成功，等待用户扫描二维码")
            else:
                logger.error("登录流程启动失败")
                QMessageBox.warning(self.main_window, "登录失败", "启动登录失败，请检查网络连接和Node.js环境")

        except Exception as e:
            logger.exception(f"启动登录时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"启动登录失败: {str(e)}")

    def logout_whatsapp(self):
        """退出WhatsApp登录"""
        try:
            logger.info("用户点击退出登录按钮")
            success = self.login_manager.logout()
            if success:
                self.update_ui_state()
                QMessageBox.information(self.main_window, "已退出", "已退出WhatsApp登录")
            else:
                QMessageBox.warning(self.main_window, "退出失败", "退出登录失败")
        except Exception as e:
            logger.exception(f"退出登录时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"退出登录失败: {str(e)}")

    def reset_login_status(self):
        """重置登录状态"""
        try:
            logger.info("用户点击重置登录状态按钮")

            # 强制退出当前登录
            self.login_manager.logout()

            # 清理状态文件
            import os
            login_status_file = self.login_manager.login_status_file
            if os.path.exists(login_status_file):
                with open(login_status_file, "w", encoding="utf-8") as f:
                    import json
                    json.dump({"status": "initializing"}, f)
                logger.info("已清理登录状态文件")

            # 重置内存状态
            self.login_manager.is_logged_in = False
            self.login_manager.is_logging_in = False

            # 更新UI
            self.update_ui_state()

            QMessageBox.information(self.main_window, "重置完成", "登录状态已重置，可以重新尝试登录")

        except Exception as e:
            logger.exception(f"重置登录状态时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"重置状态失败: {str(e)}")

    # ========== 线程安全的回调函数 ==========

    def on_login_status_changed_safe(self, is_logged_in):
        """线程安全的登录状态变化回调"""
        logger.info(f"登录状态变化: {is_logged_in}")
        # 使用信号来安全地更新UI
        if is_logged_in:
            self.ui_signals.login_success.emit()
        # 总是更新UI状态
        self.update_ui_state()

    def on_qr_code_received_safe(self, qr_code):
        """线程安全的QR码接收回调"""
        logger.info("收到WhatsApp登录二维码")
        # 使用信号来安全地处理QR码
        self.ui_signals.qr_code_received.emit(qr_code)
        self.update_ui_state()

    def on_login_error_safe(self, error_message):
        """线程安全的登录错误回调"""
        logger.error(f"登录错误: {error_message}")
        # 使用信号来安全地显示错误
        self.ui_signals.login_error.emit(error_message)
        self.update_ui_state()

    # ========== UI信号处理函数（在主线程中执行）==========

    def show_login_success_message(self):
        """显示登录成功消息（主线程安全）"""
        try:
            # 不显示弹窗，只更新状态文本和日志
            logger.info("WhatsApp登录成功，群发功能已可用")

            # 更新登录信息文本为成功状态
            if hasattr(self, 'login_info'):
                self.login_info.setText("✅ WhatsApp登录成功！群发功能已可用")
                self.login_info.setStyleSheet("color: #4CAF50; font-size: 10pt; font-weight: bold;")

            # 可以添加一个临时的成功提示，几秒后自动消失
            self.show_temporary_success_message()

        except Exception as e:
            logger.error(f"显示登录成功消息时出错: {str(e)}")

    def show_temporary_success_message(self):
        """显示临时成功消息"""
        try:
            # 创建一个定时器来显示临时消息
            if hasattr(self, 'success_timer'):
                self.success_timer.stop()

            self.success_timer = QTimer()
            self.success_timer.setSingleShot(True)
            self.success_timer.timeout.connect(self.hide_temporary_success_message)

            # 显示成功消息3秒
            if hasattr(self, 'login_info'):
                self.login_info.setText("🎉 WhatsApp登录成功！现在可以开始群发消息了")
                self.login_info.setStyleSheet("color: #4CAF50; font-size: 10pt; font-weight: bold; background-color: rgba(76, 175, 80, 0.1); padding: 5px; border-radius: 3px;")

            self.success_timer.start(3000)  # 3秒后恢复正常状态

        except Exception as e:
            logger.error(f"显示临时成功消息时出错: {str(e)}")

    def hide_temporary_success_message(self):
        """隐藏临时成功消息"""
        try:
            if hasattr(self, 'login_info'):
                self.login_info.setText("WhatsApp已登录，可以开始群发消息")
                self.login_info.setStyleSheet("color: #4CAF50; font-size: 10pt; font-style: italic;")
        except Exception as e:
            logger.error(f"隐藏临时成功消息时出错: {str(e)}")

    def show_login_error_message(self, error_message):
        """显示登录错误消息（主线程安全）"""
        try:
            QMessageBox.critical(self.main_window, "登录错误", f"WhatsApp登录失败:\n{error_message}")
        except Exception as e:
            logger.error(f"显示登录错误消息时出错: {str(e)}")

    def handle_qr_code_received(self, qr_code):
        """处理QR码接收（主线程安全）"""
        try:
            # QR码已经在终端显示，这里可以添加其他处理
            logger.info(f"QR码已生成，请在浏览器中扫描: {qr_code[:20]}...")
        except Exception as e:
            logger.error(f"处理QR码时出错: {str(e)}")

    # ========== 保留原有的回调函数（用于兼容性）==========

    def on_login_status_changed(self, is_logged_in):
        """登录状态变化回调（兼容性保留）"""
        self.on_login_status_changed_safe(is_logged_in)

    def on_qr_code_received(self, qr_code):
        """收到QR码回调（兼容性保留）"""
        self.on_qr_code_received_safe(qr_code)

    def on_login_error(self, error_message):
        """登录错误回调（兼容性保留）"""
        self.on_login_error_safe(error_message)

    # ========== 群发发送相关方法 ==========

    def on_send_progress(self, progress, status_message, current_phone):
        """发送进度回调"""
        logger.info(f"发送进度: {progress}% - {current_phone} - {status_message}")

        # 更新当前发送号码的状态
        if current_phone:
            self.telenumber_ops.update_phone_status_by_number(
                current_phone, self.username, 'sending', self.store_name
            )

        # 更新表格显示
        self.update_table_display()

        # 更新按钮文本显示进度
        self.bulk_send_btn.setText(f"发送中... {progress}%")

    def on_send_completed(self, success, sent_count, total_count, errors):
        """发送完成回调"""
        logger.info(f"群发完成: 成功={success}, 发送数={sent_count}/{total_count}")

        # 更新所有号码的状态
        phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)

        # 创建错误号码集合
        error_phones = {error.get('phone', '') for error in errors}

        for record in phone_records:
            if record['phone_number'] in error_phones:
                self.telenumber_ops.update_phone_status(record['id'], 'failed')
            else:
                self.telenumber_ops.update_phone_status(record['id'], 'sent')

        # 更新UI
        self.update_table_display()
        self.update_ui_state()

        # 显示结果
        if errors:
            error_details = "\n".join([f"• {error.get('phone', '')}: {error.get('error', '')}" for error in errors[:5]])
            if len(errors) > 5:
                error_details += f"\n... 还有 {len(errors) - 5} 个错误"

            QMessageBox.warning(
                self.main_window,
                "群发完成（有错误）",
                f"群发完成！\n\n成功发送: {sent_count}/{total_count}\n失败: {len(errors)}\n\n失败详情:\n{error_details}"
            )
        else:
            QMessageBox.information(
                self.main_window,
                "群发完成",
                f"群发完成！\n\n全部发送成功: {sent_count}/{total_count}"
            )

    def on_send_error(self, error_message):
        """发送错误回调"""
        logger.error(f"群发错误: {error_message}")

        # 恢复所有号码状态为待发送
        phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
        for record in phone_records:
            if record['status'] == 'sending':
                self.telenumber_ops.update_phone_status(record['id'], 'pending')

        # 更新UI
        self.update_table_display()
        self.update_ui_state()

        QMessageBox.critical(self.main_window, "发送错误", f"群发过程中出错:\n{error_message}")

    # ========== 表格操作相关方法 ==========

    def show_table_context_menu(self, position):
        """显示表格右键菜单"""
        try:
            from PySide6.QtWidgets import QMenu

            item = self.phone_numbers_table.itemAt(position)
            if item is None:
                return

            row = item.row()
            if row < 0:
                return

            # 创建右键菜单
            menu = QMenu(self.phone_numbers_table)

            delete_action = menu.addAction("删除号码")
            reset_action = menu.addAction("重置状态")

            # 显示菜单
            action = menu.exec_(self.phone_numbers_table.mapToGlobal(position))

            if action == delete_action:
                self.delete_phone_number_at_row(row)
            elif action == reset_action:
                self.reset_phone_status_at_row(row)

        except Exception as e:
            logger.error(f"显示右键菜单时出错: {str(e)}")

    def delete_phone_number_at_row(self, row):
        """删除指定行的电话号码"""
        try:
            # 获取记录ID
            item = self.phone_numbers_table.item(row, 0)
            if not item:
                return

            record_id = item.data(Qt.UserRole)
            if not record_id:
                return

            # 获取电话号码用于确认
            phone_item = self.phone_numbers_table.item(row, 1)
            phone_number = phone_item.text() if phone_item else "未知号码"

            # 确认删除
            reply = QMessageBox.question(
                self.main_window,
                "确认删除",
                f"确定要删除电话号码 {phone_number} 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                success = self.telenumber_ops.delete_phone_number(record_id)
                if success:
                    self.update_table_display()
                    self.update_ui_state()
                    logger.info(f"删除电话号码: {phone_number}")
                else:
                    QMessageBox.warning(self.main_window, "删除失败", "删除电话号码失败")

        except Exception as e:
            logger.error(f"删除电话号码时出错: {str(e)}")

    def reset_phone_status_at_row(self, row):
        """重置指定行的电话号码状态"""
        try:
            # 获取记录ID
            item = self.phone_numbers_table.item(row, 0)
            if not item:
                return

            record_id = item.data(Qt.UserRole)
            if not record_id:
                return

            # 重置状态为待发送
            success = self.telenumber_ops.update_phone_status(record_id, 'pending')
            if success:
                self.update_table_display()
                logger.info(f"重置电话号码状态: ID {record_id}")
            else:
                QMessageBox.warning(self.main_window, "重置失败", "重置状态失败")

        except Exception as e:
            logger.error(f"重置电话号码状态时出错: {str(e)}")

    def clear_phone_numbers(self):
        """清空电话号码列表"""
        try:
            reply = QMessageBox.question(
                self.main_window,
                "确认清空",
                "确定要清空所有电话号码吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                success = self.telenumber_ops.clear_phone_numbers(self.username, self.store_name)
                if success:
                    self.update_table_display()
                    self.update_ui_state()
                    logger.info("清空电话号码列表")
                else:
                    QMessageBox.warning(self.main_window, "清空失败", "清空电话号码失败")

        except Exception as e:
            logger.error(f"清空电话号码时出错: {str(e)}")
