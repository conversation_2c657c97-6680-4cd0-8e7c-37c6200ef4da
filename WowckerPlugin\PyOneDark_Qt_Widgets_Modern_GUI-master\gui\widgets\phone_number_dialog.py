# -*- coding: utf-8 -*-

"""
电话号码管理对话框
支持国家代码选择、电话号码输入、批量添加等功能
"""

import sys
import os
import re
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QComboBox, QTextEdit, QFrame, QMessageBox,
    QScrollArea, QWidget, QGridLayout, QSizePolicy, QSpacerItem
)

# 国家代码数据（包含国旗emoji和国家代码）
COUNTRY_CODES = [
    {"name": "中国", "code": "+86", "flag": "🇨🇳", "whatsapp_code": "86"},
    {"name": "美国", "code": "+1", "flag": "🇺🇸", "whatsapp_code": "1"},
    {"name": "英国", "code": "+44", "flag": "🇬🇧", "whatsapp_code": "44"},
    {"name": "日本", "code": "+81", "flag": "🇯🇵", "whatsapp_code": "81"},
    {"name": "韩国", "code": "+82", "flag": "🇰🇷", "whatsapp_code": "82"},
    {"name": "德国", "code": "+49", "flag": "🇩🇪", "whatsapp_code": "49"},
    {"name": "法国", "code": "+33", "flag": "🇫🇷", "whatsapp_code": "33"},
    {"name": "澳大利亚", "code": "+61", "flag": "🇦🇺", "whatsapp_code": "61"},
    {"name": "加拿大", "code": "+1", "flag": "🇨🇦", "whatsapp_code": "1"},
    {"name": "新加坡", "code": "+65", "flag": "🇸🇬", "whatsapp_code": "65"},
    {"name": "印度", "code": "+91", "flag": "🇮🇳", "whatsapp_code": "91"},
    {"name": "巴西", "code": "+55", "flag": "🇧🇷", "whatsapp_code": "55"},
    {"name": "俄罗斯", "code": "+7", "flag": "🇷🇺", "whatsapp_code": "7"},
    {"name": "意大利", "code": "+39", "flag": "🇮🇹", "whatsapp_code": "39"},
    {"name": "西班牙", "code": "+34", "flag": "🇪🇸", "whatsapp_code": "34"},
    {"name": "荷兰", "code": "+31", "flag": "🇳🇱", "whatsapp_code": "31"},
    {"name": "瑞士", "code": "+41", "flag": "🇨🇭", "whatsapp_code": "41"},
    {"name": "瑞典", "code": "+46", "flag": "🇸🇪", "whatsapp_code": "46"},
    {"name": "挪威", "code": "+47", "flag": "🇳🇴", "whatsapp_code": "47"},
    {"name": "丹麦", "code": "+45", "flag": "🇩🇰", "whatsapp_code": "45"},
]

class PhoneNumberDialog(QDialog):
    """电话号码管理对话框"""
    
    # 信号：电话号码添加成功
    phone_added = Signal(str, str)  # (formatted_number, whatsapp_format)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加电话号码")
        self.setModal(True)
        self.resize(500, 600)
        
        # 设置深色主题样式
        self.setStyleSheet("""
            QDialog {
                background-color: #1b1e23;
                color: #f8f8f2;
            }
            QLabel {
                color: #f8f8f2;
                font-size: 12pt;
            }
            QLineEdit, QTextEdit {
                background-color: #2c313c;
                color: #f8f8f2;
                border: 1px solid #3c4454;
                border-radius: 6px;
                padding: 8px;
                font-size: 11pt;
            }
            QLineEdit:focus, QTextEdit:focus {
                border: 2px solid #5e7ce0;
            }
            QComboBox {
                background-color: #2c313c;
                color: #f8f8f2;
                border: 1px solid #3c4454;
                border-radius: 6px;
                padding: 8px;
                font-size: 11pt;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #f8f8f2;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: #2c313c;
                color: #f8f8f2;
                border: 1px solid #3c4454;
                selection-background-color: #5e7ce0;
            }
            QPushButton {
                background-color: #5e7ce0;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 11pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4a6bc8;
            }
            QPushButton:pressed {
                background-color: #3f5ba9;
            }
            QPushButton:disabled {
                background-color: #3c4454;
                color: #8a8a8a;
            }
            QFrame {
                background-color: #2c313c;
                border-radius: 8px;
            }
        """)
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("添加电话号码")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 单个号码添加区域
        single_frame = QFrame()
        single_layout = QVBoxLayout(single_frame)
        single_layout.setContentsMargins(15, 15, 15, 15)
        single_layout.setSpacing(15)
        
        # 单个号码标题
        single_title = QLabel("单个号码添加")
        single_title.setStyleSheet("font-weight: bold; font-size: 13pt;")
        single_layout.addWidget(single_title)
        
        # 国家代码选择
        country_layout = QHBoxLayout()
        country_label = QLabel("国家/地区:")
        country_label.setMinimumWidth(80)
        self.country_combo = QComboBox()
        self.country_combo.setMinimumHeight(35)
        
        # 添加国家选项
        for country in COUNTRY_CODES:
            display_text = f"{country['flag']} {country['name']} ({country['code']})"
            self.country_combo.addItem(display_text, country)
        
        # 设置默认选择为中国
        self.country_combo.setCurrentIndex(0)
        
        country_layout.addWidget(country_label)
        country_layout.addWidget(self.country_combo)
        single_layout.addLayout(country_layout)
        
        # 电话号码输入
        phone_layout = QHBoxLayout()
        phone_label = QLabel("电话号码:")
        phone_label.setMinimumWidth(80)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("请输入电话号码（不含国家代码）")
        self.phone_input.setMinimumHeight(35)
        
        phone_layout.addWidget(phone_label)
        phone_layout.addWidget(self.phone_input)
        single_layout.addLayout(phone_layout)
        
        # 单个添加按钮
        self.add_single_btn = QPushButton("添加此号码")
        self.add_single_btn.clicked.connect(self.add_single_number)
        single_layout.addWidget(self.add_single_btn)
        
        layout.addWidget(single_frame)
        
        # 批量添加区域
        batch_frame = QFrame()
        batch_layout = QVBoxLayout(batch_frame)
        batch_layout.setContentsMargins(15, 15, 15, 15)
        batch_layout.setSpacing(15)
        
        # 批量添加标题
        batch_title = QLabel("批量号码添加")
        batch_title.setStyleSheet("font-weight: bold; font-size: 13pt;")
        batch_layout.addWidget(batch_title)
        
        # 批量输入说明
        batch_info = QLabel("每行一个号码，支持格式：\n• +86 13812345678\n• 86 13812345678\n• 13812345678（使用上方选择的国家代码）")
        batch_info.setStyleSheet("color: #a0a0a0; font-size: 10pt;")
        batch_layout.addWidget(batch_info)
        
        # 批量输入框
        self.batch_input = QTextEdit()
        self.batch_input.setPlaceholderText("请输入电话号码，每行一个...")
        self.batch_input.setMinimumHeight(120)
        batch_layout.addWidget(self.batch_input)
        
        # 批量添加按钮
        self.add_batch_btn = QPushButton("批量添加")
        self.add_batch_btn.clicked.connect(self.add_batch_numbers)
        batch_layout.addWidget(self.add_batch_btn)
        
        layout.addWidget(batch_frame)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        button_layout.addWidget(self.close_btn)
        layout.addLayout(button_layout)
        
    def add_single_number(self):
        """添加单个电话号码"""
        phone_number = self.phone_input.text().strip()
        if not phone_number:
            QMessageBox.warning(self, "警告", "请输入电话号码")
            return
            
        # 获取选择的国家信息
        country_data = self.country_combo.currentData()
        
        # 格式化和验证号码
        formatted_number, whatsapp_format = self.format_phone_number(phone_number, country_data)
        
        if formatted_number and whatsapp_format:
            # 发送信号
            self.phone_added.emit(formatted_number, whatsapp_format)
            
            # 清空输入框
            self.phone_input.clear()
            
            QMessageBox.information(self, "成功", f"电话号码添加成功：{formatted_number}")
        else:
            QMessageBox.warning(self, "错误", "电话号码格式不正确")
    
    def add_batch_numbers(self):
        """批量添加电话号码"""
        text = self.batch_input.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入电话号码")
            return
            
        lines = text.split('\n')
        country_data = self.country_combo.currentData()
        
        success_count = 0
        error_count = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 格式化和验证号码
            formatted_number, whatsapp_format = self.format_phone_number(line, country_data)
            
            if formatted_number and whatsapp_format:
                # 发送信号
                self.phone_added.emit(formatted_number, whatsapp_format)
                success_count += 1
            else:
                error_count += 1
        
        # 清空输入框
        self.batch_input.clear()
        
        # 显示结果
        if success_count > 0:
            message = f"成功添加 {success_count} 个电话号码"
            if error_count > 0:
                message += f"，{error_count} 个号码格式错误被跳过"
            QMessageBox.information(self, "完成", message)
        else:
            QMessageBox.warning(self, "错误", "没有有效的电话号码")
    
    def format_phone_number(self, phone_number, country_data):
        """
        格式化电话号码
        返回: (用户友好格式, WhatsApp格式)
        """
        try:
            # 清理输入
            cleaned = re.sub(r'[^\d+]', '', phone_number.strip())
            
            if not cleaned:
                return None, None
            
            # 处理不同格式
            if cleaned.startswith('+'):
                # 已包含国家代码的格式 +8613812345678
                cleaned = cleaned[1:]  # 移除+号
                whatsapp_format = f"{cleaned}@c.us"
                
                # 格式化显示
                if cleaned.startswith('86') and len(cleaned) == 13:
                    # 中国号码
                    number_part = cleaned[2:]
                    formatted = f"+86 {number_part[:3]} {number_part[3:7]} {number_part[7:]}"
                else:
                    # 其他国家号码
                    formatted = f"+{cleaned}"
                    
            elif len(cleaned) > 11:
                # 可能是包含国家代码但没有+号的格式 8613812345678
                whatsapp_format = f"{cleaned}@c.us"
                
                if cleaned.startswith('86') and len(cleaned) == 13:
                    # 中国号码
                    number_part = cleaned[2:]
                    formatted = f"+86 {number_part[:3]} {number_part[3:7]} {number_part[7:]}"
                else:
                    formatted = f"+{cleaned}"
                    
            else:
                # 本地号码格式，需要添加国家代码
                country_code = country_data['whatsapp_code']
                whatsapp_format = f"{country_code}{cleaned}@c.us"
                
                if country_code == '86' and len(cleaned) == 11:
                    # 中国手机号码
                    formatted = f"+86 {cleaned[:3]} {cleaned[3:7]} {cleaned[7:]}"
                else:
                    formatted = f"{country_data['code']} {cleaned}"
            
            return formatted, whatsapp_format
            
        except Exception as e:
            print(f"格式化电话号码时出错: {e}")
            return None, None
