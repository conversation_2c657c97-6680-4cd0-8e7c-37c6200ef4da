"""
调试群发获客按钮点击问题
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master'))

def debug_button_connection():
    """调试按钮连接问题"""
    print("开始调试群发获客按钮连接...")
    print("=" * 50)
    
    try:
        # 检查主窗口文件
        main_file = os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master', 'main.py')
        
        if os.path.exists(main_file):
            print("✓ 主窗口文件存在")
            
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查按钮连接代码
                if 'btn_bulk_customer_acquisition' in content:
                    print("✓ 主窗口文件包含群发获客按钮处理代码")
                    
                    # 检查具体的连接代码
                    if 'show_bulk_customer_acquisition' in content:
                        print("✓ 找到show_bulk_customer_acquisition方法")
                    else:
                        print("✗ 缺少show_bulk_customer_acquisition方法")
                        
                    if 'button.clicked.connect(self.show_bulk_customer_acquisition)' in content:
                        print("✓ 找到按钮信号连接代码")
                    else:
                        print("✗ 缺少按钮信号连接代码")
                        
                    if 'MainFunctions.set_page(self, self.ui.load_pages.page_9)' in content:
                        print("✓ 找到页面切换代码")
                    else:
                        print("✗ 缺少页面切换代码")
                        
                else:
                    print("✗ 主窗口文件缺少群发获客按钮处理代码")
        else:
            print("✗ 主窗口文件不存在")
            
        # 检查菜单配置文件
        setup_file = os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master', 'gui', 'uis', 'windows', 'main_window', 'setup_main_window.py')
        
        if os.path.exists(setup_file):
            print("✓ 菜单配置文件存在")
            
            with open(setup_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                if '"btn_id" : "btn_bulk_customer_acquisition"' in content:
                    print("✓ 菜单配置包含群发获客按钮ID")
                else:
                    print("✗ 菜单配置缺少群发获客按钮ID")
                    
                if '"btn_text" : "群发获客"' in content:
                    print("✓ 菜单配置包含群发获客按钮文本")
                else:
                    print("✗ 菜单配置缺少群发获客按钮文本")
        else:
            print("✗ 菜单配置文件不存在")
            
        # 检查页面文件
        pages_file = os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master', 'gui', 'uis', 'pages', 'ui_main_pages.py')
        
        if os.path.exists(pages_file):
            print("✓ 页面文件存在")
            
            with open(pages_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                if 'self.page_9 = QWidget()' in content:
                    print("✓ 页面文件包含page_9定义")
                else:
                    print("✗ 页面文件缺少page_9定义")
                    
                if 'self.pages.addWidget(self.page_9)' in content:
                    print("✓ 页面文件包含page_9添加到堆栈的代码")
                else:
                    print("✗ 页面文件缺少page_9添加到堆栈的代码")
                    
                if 'bulk_acquisition_title' in content:
                    print("✓ 页面文件包含群发获客UI组件")
                else:
                    print("✗ 页面文件缺少群发获客UI组件")
        else:
            print("✗ 页面文件不存在")
            
        # 检查控制器文件
        controller_file = os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master', 'gui', 'core', 'bulk_customer_acquisition_controller.py')
        
        if os.path.exists(controller_file):
            print("✓ 控制器文件存在")
        else:
            print("✗ 控制器文件不存在")
            
        print("-" * 50)
        print("调试完成！")
        
    except Exception as e:
        print(f"✗ 调试过程中发生错误: {e}")

def check_button_object_names():
    """检查按钮对象名称"""
    print("\n检查按钮对象名称...")
    print("=" * 30)
    
    try:
        setup_file = os.path.join(project_root, 'PyOneDark_Qt_Widgets_Modern_GUI-master', 'gui', 'uis', 'windows', 'main_window', 'setup_main_window.py')
        
        if os.path.exists(setup_file):
            with open(setup_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 提取所有按钮ID
                import re
                button_ids = re.findall(r'"btn_id"\s*:\s*"([^"]+)"', content)
                
                print("找到的按钮ID:")
                for btn_id in button_ids:
                    print(f"  - {btn_id}")
                    
                if 'btn_bulk_customer_acquisition' in button_ids:
                    print("✓ 群发获客按钮ID存在于配置中")
                else:
                    print("✗ 群发获客按钮ID不存在于配置中")
                    
    except Exception as e:
        print(f"✗ 检查按钮对象名称时发生错误: {e}")

def generate_fix_suggestions():
    """生成修复建议"""
    print("\n修复建议:")
    print("=" * 30)
    
    print("1. 确保按钮信号连接正确:")
    print("   在main.py的__init__方法中添加:")
    print("   elif button.objectName() == \"btn_bulk_customer_acquisition\":")
    print("       button.clicked.connect(self.show_bulk_customer_acquisition)")
    print()
    
    print("2. 确保处理方法存在:")
    print("   在main.py中添加:")
    print("   def show_bulk_customer_acquisition(self):")
    print("       self.ui.left_menu.select_only_one(\"btn_bulk_customer_acquisition\")")
    print("       MainFunctions.set_page(self, self.ui.load_pages.page_9)")
    print()
    
    print("3. 确保页面正确添加到堆栈:")
    print("   在ui_main_pages.py中确保有:")
    print("   self.pages.addWidget(self.page_9)")
    print()
    
    print("4. 调试步骤:")
    print("   - 在按钮点击方法中添加print语句")
    print("   - 检查控制台输出")
    print("   - 验证按钮objectName是否正确")
    print("   - 确认页面切换逻辑是否执行")

if __name__ == "__main__":
    debug_button_connection()
    check_button_object_names()
    generate_fix_suggestions()
