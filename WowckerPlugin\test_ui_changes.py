#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI更改的脚本
验证群发获客页面的新布局是否正确
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_ui_imports():
    """测试UI相关的导入是否正常"""
    try:
        # 测试基本的UI页面导入
        sys.path.append(str(project_root / "PyOneDark_Qt_Widgets_Modern_GUI-master"))
        
        # 测试页面导入
        from gui.uis.pages.ui_main_pages import Ui_MainPages
        print("✓ UI页面导入成功")
        
        # 测试控制器导入
        from gui.core.bulk_customer_acquisition_controller import BulkCustomerAcquisitionController
        print("✓ 群发获客控制器导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_ui_components():
    """测试UI组件是否正确定义"""
    try:
        sys.path.append(str(project_root / "PyOneDark_Qt_Widgets_Modern_GUI-master"))
        from gui.uis.pages.ui_main_pages import Ui_MainPages
        
        # 创建一个模拟的主页面对象
        class MockMainPages:
            def __init__(self):
                pass
        
        mock_pages = MockMainPages()
        ui = Ui_MainPages()
        
        # 检查是否有setupUi方法
        if hasattr(ui, 'setupUi'):
            print("✓ setupUi方法存在")
        else:
            print("✗ setupUi方法不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ UI组件测试失败: {e}")
        return False

def test_database_operations():
    """测试数据库操作是否正常"""
    try:
        from database.telenumber_operations import TeleNumberOperations
        print("✓ 电话号码数据库操作导入成功")
        
        # 测试创建实例
        ops = TeleNumberOperations()
        print("✓ 电话号码操作实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库操作测试失败: {e}")
        return False

def test_whatsapp_modules():
    """测试WhatsApp相关模块"""
    try:
        from chat.whatsapp.bulk_login_manager import WhatsAppBulkLoginManager
        print("✓ WhatsApp登录管理器导入成功")
        
        from chat.whatsapp.bulk_sender import WhatsAppBulkSender
        print("✓ WhatsApp群发发送器导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ WhatsApp模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试UI更改...")
    print("=" * 50)
    
    tests = [
        ("UI导入测试", test_ui_imports),
        ("UI组件测试", test_ui_components),
        ("数据库操作测试", test_database_operations),
        ("WhatsApp模块测试", test_whatsapp_modules),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！UI更改应该是正确的。")
        return True
    else:
        print("⚠️  部分测试失败，可能需要检查依赖或配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
