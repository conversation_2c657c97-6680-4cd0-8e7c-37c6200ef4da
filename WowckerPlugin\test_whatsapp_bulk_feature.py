# -*- coding: utf-8 -*-

"""
WhatsApp批量消息功能测试脚本
用于验证新实现的功能是否正常工作
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_phone_number_utils():
    """测试电话号码工具类"""
    print("=== 测试电话号码工具类 ===")
    
    try:
        from utils.phone_number_utils import PhoneNumberUtils
        
        # 测试中国号码
        test_numbers = [
            "13812345678",
            "+86 13812345678", 
            "86 13812345678",
            "*************",
            "5551234567"
        ]
        
        for number in test_numbers:
            print(f"\n原始号码: {number}")
            
            # 测试格式化显示
            display_format = PhoneNumberUtils.format_for_display(number)
            print(f"显示格式: {display_format}")
            
            # 测试WhatsApp格式
            whatsapp_format = PhoneNumberUtils.format_for_whatsapp(number)
            print(f"WhatsApp格式: {whatsapp_format}")
            
            # 测试验证
            is_valid, message = PhoneNumberUtils.validate_phone_number(number)
            print(f"验证结果: {is_valid} - {message}")
        
        print("✓ 电话号码工具类测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 电话号码工具类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_state_manager():
    """测试登录状态管理器"""
    print("\n=== 测试登录状态管理器 ===")
    
    try:
        from utils.login_state_manager import LoginStateManager
        
        # 创建管理器实例
        manager = LoginStateManager("test_user", "test_store")
        
        # 测试保存和加载状态
        print("保存登录状态...")
        manager.save_login_state(True, {"session_id": "test123"})
        
        print("加载登录状态...")
        state = manager.load_login_state()
        print(f"加载的状态: {state}")
        
        # 测试状态检查
        is_logged_in = manager.is_logged_in()
        print(f"是否已登录: {is_logged_in}")
        
        # 测试清除状态
        print("清除登录状态...")
        manager.clear_login_state()
        
        is_logged_in_after_clear = manager.is_logged_in()
        print(f"清除后是否已登录: {is_logged_in_after_clear}")
        
        print("✓ 登录状态管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 登录状态管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bulk_message_service():
    """测试批量消息服务"""
    print("\n=== 测试批量消息服务 ===")
    
    try:
        from services.bulk_message_service import BulkMessageService
        
        # 创建服务实例
        service = BulkMessageService("test_user", "test_store")
        
        # 测试会话信息
        session_info = service.get_session_info()
        print(f"会话信息: {session_info}")
        
        # 测试电话号码管理
        print("测试添加电话号码...")
        success = service.add_phone_number("<EMAIL>", "+86 138 1234 5678")
        print(f"添加号码结果: {success}")
        
        # 获取电话号码列表
        numbers = service.get_phone_numbers()
        print(f"电话号码列表: {len(numbers)} 个号码")
        
        # 清理测试数据
        if numbers:
            print("清理测试数据...")
            service.clear_all_phone_numbers()
        
        print("✓ 批量消息服务测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 批量消息服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    try:
        from database.telenumber_operations import TeleNumberOperations
        
        # 创建操作实例
        ops = TeleNumberOperations()
        
        # 测试添加电话号码
        print("测试添加电话号码...")
        result = ops.add_phone_number("<EMAIL>", "test_user", "test_store")
        print(f"添加结果: {result}")
        
        # 测试获取电话号码
        print("测试获取电话号码...")
        numbers = ops.get_phone_numbers_by_user("test_user", "test_store")
        print(f"获取到 {len(numbers)} 个号码")
        
        # 清理测试数据
        if numbers:
            print("清理测试数据...")
            for number in numbers:
                ops.delete_phone_number(number['phone_number'], "test_user", "test_store")
        
        print("✓ 数据库操作测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """测试UI组件（需要Qt环境）"""
    print("\n=== 测试UI组件 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from gui.widgets.phone_number_dialog import PhoneNumberDialog
        from gui.widgets.whatsapp_login_widget import WhatsAppLoginWidget
        from gui.widgets.bulk_message_widget import BulkMessageWidget
        
        # 创建应用实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 测试电话号码对话框
        print("创建电话号码对话框...")
        phone_dialog = PhoneNumberDialog()
        print("✓ 电话号码对话框创建成功")
        
        # 测试WhatsApp登录组件
        print("创建WhatsApp登录组件...")
        login_widget = WhatsAppLoginWidget()
        print("✓ WhatsApp登录组件创建成功")
        
        # 测试批量消息组件
        print("创建批量消息组件...")
        message_widget = BulkMessageWidget()
        print("✓ 批量消息组件创建成功")
        
        print("✓ UI组件测试通过")
        return True
        
    except Exception as e:
        print(f"✗ UI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始WhatsApp批量消息功能测试...\n")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("电话号码工具类", test_phone_number_utils()))
    test_results.append(("登录状态管理器", test_login_state_manager()))
    test_results.append(("数据库操作", test_database_operations()))
    test_results.append(("批量消息服务", test_bulk_message_service()))
    test_results.append(("UI组件", test_ui_components()))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 个测试通过, {failed} 个测试失败")
    
    if failed == 0:
        print("\n🎉 所有测试都通过了！WhatsApp批量消息功能已准备就绪。")
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，请检查相关功能。")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
