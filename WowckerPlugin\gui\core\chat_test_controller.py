"""
聊天测试控制器
提供聊天测试页面的数据和操作功能
"""
import os
import json
import sys
import logging
from typing import List, Dict, Any
from PySide6.QtCore import Qt, QSize, Signal, QObject
from PySide6.QtGui import QIcon, QPixmap, QColor, QPainter, QFont, QPen
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, 
    QPushButton, QFrame, QScrollArea, QSpacerItem, QSizePolicy,
    QApplication, QTextEdit, QMessageBox
)

from database.store_operations import StoreOperations
from database.context_operations import ContextOperations

# 导入相同的渲染组件
from gui.core.chat_records_controller import ChatRecordsController

# 配置日志
logger = logging.getLogger(__name__)

# 测试聊天使用店铺名作为chat_id
# 用前缀区分测试聊天和实际聊天
TEST_CHAT_PREFIX = "test_"  # 测试聊天的前缀

class ChatTestController(QObject):
    """聊天测试控制器"""
    
    def __init__(self, main_window):
        """
        初始化聊天测试控制器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.store_ops = StoreOperations()
        self.context_ops = ContextOperations()
        
        # 缓存数据
        self.stores = []
        self.current_store = None
        
        # 初始化UI引用
        self.ui = main_window.ui.load_pages
        self.store_combo = self.ui.chat_test_store_combo
        self.chat_container = self.ui.chat_test_container
        self.input_field = self.ui.chat_test_input
        self.send_button = self.ui.chat_test_send_btn
        self.delete_button = self.ui.chat_test_delete_btn
        
        # 确保聊天内容布局引用已初始化
        self.chat_content_layout = None
        
        # 连接信号
        self.connect_signals()
        
        # 导入聊天模块 - 在初始化时延迟导入以避免循环引用
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'chat'))
        
    def connect_signals(self):
        """连接信号到槽函数"""
        self.store_combo.currentIndexChanged.connect(self.on_store_changed)
        self.send_button.clicked.connect(self.send_message)
        self.input_field.returnPressed.connect(self.send_message)
        self.delete_button.clicked.connect(self.delete_chat_history)
        
    def load_stores(self):
        """加载店铺列表"""
        try:
            # 获取所有店铺
            self.stores = self.store_ops.get_all_stores()
            
            # 清空并重新填充下拉框
            self.store_combo.clear()
            
            # 添加店铺到下拉框
            for store in self.stores:
                self.store_combo.addItem(store['plg_shopname'])
                
            # 如果有店铺，则选择第一个
            if self.stores:
                self.current_store = self.stores[0]['plg_shopname']
        except Exception as e:
            logger.error(f"加载店铺列表失败: {str(e)}")
    
    def on_store_changed(self, index):
        """
        店铺选择变更事件处理
        
        Args:
            index: 选择的索引
        """
        if index >= 0 and index < len(self.stores):
            self.current_store = self.stores[index]['plg_shopname']
            logger.info(f"当前选择的店铺: {self.current_store}")
            
            # 加载该店铺的测试聊天记录
            self.load_test_chat_history()
    
    def load_test_chat_history(self):
        """加载测试聊天历史记录"""
        if not self.current_store:
            return
            
        try:
            # 从数据库加载聊天历史
            # 使用店铺名称作为chat_id，并添加测试前缀
            test_chat_id = f"{TEST_CHAT_PREFIX}{self.current_store}"
            context = self.context_ops.get_context(
                user_name="current_user",
                store_name=self.current_store,
                chat_id=test_chat_id
            )
            
            # 清空聊天容器
            self.clear_chat_container()
            
            # 如果有聊天记录，则显示
            if context and 'context' in context:
                logger.info(f"找到聊天记录: {context['context'][:100]}...")
                self.parse_chat_history(context['context'])
                
                # 立即刷新并滚动到底部
                QApplication.processEvents()
                self._scroll_to_bottom()
            else:
                logger.info("未找到聊天记录，显示欢迎消息")
                # 添加欢迎消息
                welcome_label = QLabel("这是一个新的聊天测试会话，请发送消息开始测试")
                welcome_label.setStyleSheet("color: #6C7693; font-size: 13px;")
                welcome_label.setAlignment(Qt.AlignCenter)
                welcome_label.setWordWrap(True)
                self.chat_content_layout.addWidget(welcome_label)
                
                # 立即刷新并滚动到底部
                QApplication.processEvents()
                self._scroll_to_bottom()
                
        except Exception as e:
            logger.error(f"加载测试聊天历史记录失败: {str(e)}")
    
    def parse_chat_history(self, text):
        """
        解析聊天历史记录
        
        Args:
            text: 聊天历史文本
        """
        try:
            # 使用行分割方式解析完整的对话内容
            lines = text.strip().split('\n')
            current_speaker = None
            current_message = ""
            
            for line in lines:
                # 处理用户消息的不同格式 - 优先使用新格式，兼容旧格式
                if "客户的历史提问：" in line or "用户历史问题：" in line:
                    # 如果已经有消息累积，先添加之前的消息
                    if current_speaker and current_message:
                        self.add_message_bubble(current_message, is_user=(current_speaker == "user"))

                    current_speaker = "user"
                    # 优先使用新格式，兼容旧格式
                    if "客户的历史提问：" in line:
                        current_message = line.split("客户的历史提问：", 1)[1].strip()
                    else:  # 兼容旧格式
                        current_message = line.split("用户历史问题：", 1)[1].strip()

                    logger.info(f"解析到用户消息: {current_message[:30]}...")
                
                # 处理AI消息的不同格式
                elif "你的历史回答：" in line:
                    # 如果已经有消息累积，先添加之前的消息
                    if current_speaker and current_message:
                        self.add_message_bubble(current_message, is_user=(current_speaker == "user"))
                    
                    current_speaker = "assistant"
                    current_message = line.split("你的历史回答：", 1)[1].strip()
                    logger.info(f"解析到AI消息: {current_message[:30]}...")
                
                # 相同发言者的消息继续
                elif current_speaker is not None:
                    # 如果是同一个人的多行消息，追加到当前消息
                    current_message += "\n" + line
            
            # 添加最后一条消息
            if current_speaker and current_message:
                self.add_message_bubble(current_message, is_user=(current_speaker == "user"))
                
        except Exception as e:
            logger.error(f"解析聊天历史记录失败: {str(e)}")
    
    def send_message(self):
        """发送消息"""
        if not self.current_store:
            logger.warning("未选择店铺，无法发送消息")
            return
            
        # 获取输入框内容
        message = self.input_field.text().strip()
        if not message:
            logger.warning("消息内容为空，不发送")
            return
            
        logger.info(f"发送消息: {message}")
        
        # 清空输入框
        self.input_field.clear()
        
        # 显示用户消息
        self.add_message_bubble(message, is_user=True)
        
        try:
            # 导入聊天处理模块
            from chat.chat_interface import ChatInterface
            
            # 获取店铺信息
            store_info = None
            for store in self.stores:
                if store['plg_shopname'] == self.current_store:
                    store_info = store
                    break
            
            if not store_info:
                logger.error(f"找不到店铺信息: {self.current_store}")
                return
                
            # 检查店铺状态，如果状态为0（关闭或付款逾期），则显示弹窗并返回
            if store_info.get('plg_status', 1) == 0:
                error_message = "店铺已关闭或需要在平台上重新启动或可能您已经欠费了请在平台上充值，请联系管理员。"
                logger.warning(f"店铺{self.current_store}状态已关闭(plg_status=0)，拒绝处理消息")
                QMessageBox.warning(self.main_window, "店铺状态异常", error_message)
                return
                
            # 构建消息对象
            # 使用店铺名称作为chat_id，并添加测试前缀
            test_chat_id = f"{TEST_CHAT_PREFIX}{self.current_store}"
            test_message = {
                "from": test_chat_id,
                "body": message,
                "timestamp": "123456789",
                "hasMedia": False
            }
                
            # 将消息保存到数据库
            # self.save_message_to_db(message, is_user=True)
            
            # 将handle_test_message函数直接导入，确保正确的导入路径
            try:
                from chat.whatsapp.chat_service import handle_test_message
            except ImportError:
                # 如果无法直接导入，使用完整路径
                logger.info("使用完整路径导入handle_test_message")
                import os
                import sys
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                sys.path.insert(0, project_root)
                from chat.whatsapp.chat_service import handle_test_message
            
            # 异步处理消息并获取回复
            # 这里简化处理，同步调用
            # 使用店铺名称作为chat_id
            reply = handle_test_message(self.current_store, test_chat_id, message)
            if not reply:
                logger.info(f"等待人工回复: {message}")
                return
            
            if reply:
                # 显示AI回复
                self.add_message_bubble(reply, is_user=False)
                # 保存回复到数据库
                # self.save_message_to_db(reply, is_user=False)
            else:
                # 显示错误信息
                self.add_message_bubble("处理消息失败，请稍后再试", is_user=False)
                
        except ImportError as e:
            logger.error(f"导入聊天模块失败: {str(e)}")
            # 显示错误信息
            self.add_message_bubble(f"导入聊天模块失败: {str(e)}", is_user=False)
        except Exception as e:
            logger.error(f"处理消息失败: {str(e)}")
            # 显示错误信息
            self.add_message_bubble(f"处理消息失败: {str(e)}", is_user=False)
    
    def save_message_to_db(self, message, is_user=True):
        """
        保存消息到数据库
        
        Args:
            message: 消息内容
            is_user: 是否是用户消息
        """
        try:
            # 使用店铺名称作为chat_id，并添加测试前缀
            test_chat_id = f"{TEST_CHAT_PREFIX}{self.current_store}"
            
            # 获取现有上下文
            context = self.context_ops.get_context(
                user_name="current_user",
                store_name=self.current_store,
                chat_id=test_chat_id
            )
            
            # 构建新的上下文内容
            new_context = ""
            if context and 'context' in context:
                new_context = context['context']
            
            # 添加新消息 - 统一使用与WhatsApp消息相同的格式前缀
            if is_user:
                if new_context:
                    new_context += f"\n客户的历史提问：{message}"
                else:
                    new_context = f"\n客户的历史提问：{message}"
            else:
                new_context += f"\n你的历史回答：{message}"
            
            # 保存到数据库 - 正确的方法名是add_context而不是save_context
            self.context_ops.add_context(
                user_name="current_user",
                store_name=self.current_store,
                chat_id=test_chat_id,
                context=new_context,
                context_trans=new_context
            )
            
            logger.info(f"成功保存消息到数据库，使用了记录ID: {test_chat_id}")
            
        except Exception as e:
            logger.error(f"保存消息到数据库失败: {str(e)}")
    
    def clear_chat_container(self):
        """清空聊天容器"""
        try:
            logger.info("准备清空聊天容器: UI布局类型=" + self.chat_container.__class__.__name__)
            
            # 清空原有布局
            if self.chat_container.layout():
                QWidget().setLayout(self.chat_container.layout())
            
            # 创建新的垂直布局
            self.chat_content_layout = QVBoxLayout(self.chat_container)
            self.chat_content_layout.setObjectName("chat_test_container_layout")
            self.chat_content_layout.setAlignment(Qt.AlignTop)
            self.chat_content_layout.setSpacing(15)  # 增加消息间间隔
            self.chat_content_layout.setContentsMargins(5, 5, 5, 5)
            
            logger.info("成功清空聊天容器")
            
        except Exception as e:
            logger.error(f"清空聊天容器失败: {str(e)}")
    
    def add_message_bubble(self, message, is_user=True):
        """
        添加消息气泡
        
        Args:
            message: 消息内容
            is_user: 是否是用户消息 (True=用户/左侧, False=AI/右侧)
        """
        logger.info(f"添加消息气泡: {'用户消息(左侧)' if is_user else 'AI消息(右侧)'}")
        
        # 创建消息容器
        container = QFrame()
        container.setStyleSheet("background: transparent; border: none;")
        
        # 创建水平布局
        h_layout = QHBoxLayout(container)
        h_layout.setContentsMargins(5, 5, 5, 5)
        
        # 获取当前容器可用宽度
        container_width = self.chat_container.width() if self.chat_container else 600
        # 消息气泡最大宽度，需要考虑头像和布局的空间
        # 确保气泡不会超过容器宽度的70%
        max_bubble_width = min(int(container_width * 0.7), 450)
        
        # 计算文本需要的宽度
        # 中文和英文字符有不同宽度，进行精细计算
        if "\n" in message:
            lines = message.split("\n")
            # 计算每行宽度 - 中文字符宽度16px，英文和数字平8px
            line_widths = []
            for line in lines:
                # 简单估算宽度：中文字符占据更宽空间
                line_width = sum(16 if '\u4e00' <= ch <= '\u9fff' else 8 for ch in line)
                line_widths.append(line_width)
            text_width = max(line_widths)
        else:
            # 单行消息
            text_width = sum(16 if '\u4e00' <= ch <= '\u9fff' else 8 for ch in message)
        
        # 消息气泡的最终宽度，考虑内边距和宽度范围
        # 确保最小宽度100px，不超过最大宽度
        padding = 40  # 内边距和滚动条额外空间
        bubble_width = min(max(text_width + padding, 120), max_bubble_width)
        
        if is_user:  # 用户消息 - 左侧
            # 用户头像
            user_avatar = QLabel()
            user_avatar.setFixedSize(36, 36)  # 稍微增大头像
            user_avatar.setStyleSheet("""
                background-color: #4C5561; 
                border-radius: 18px;
                color: white;
                font-weight: bold;
                font-size: 16px;
            """)
            user_avatar.setText("U")
            user_avatar.setAlignment(Qt.AlignCenter)
            h_layout.addWidget(user_avatar)
            h_layout.setAlignment(user_avatar, Qt.AlignTop)  # 头像靠上对齐
            
            # 添加右侧留白确保气泡靠左
            h_layout.addSpacing(5)  # 头像和气泡之间的间距
            
            # 用户消息气泡 - 浅灰色背景黑色文字
            bubble_color = "#E9E9EB"
            text_color = "#000000"
            bubble = self._create_message_bubble(message, bubble_color, text_color, bubble_width)
            h_layout.addWidget(bubble)
            h_layout.setAlignment(bubble, Qt.AlignLeft)  # 气泡靠左
            
            # 添加右侧的弹性留白
            h_layout.addStretch(1)
            
        else:  # AI消息 - 右侧
            # 添加左侧的弹性留白
            h_layout.addStretch(1)
            
            # AI消息气泡 - 蓝色背景白色文字
            bubble_color = "#007AFF" 
            text_color = "#FFFFFF"
            bubble = self._create_message_bubble(message, bubble_color, text_color, bubble_width)
            h_layout.addWidget(bubble)
            h_layout.setAlignment(bubble, Qt.AlignRight)  # 气泡靠右
            
            # AI头像与气泡之间的间距
            h_layout.addSpacing(5)
            
            # AI头像
            ai_avatar = QLabel()
            ai_avatar.setFixedSize(36, 36)  # 稍微增大头像
            ai_avatar.setStyleSheet("""
                background-color: #5E5CE6; 
                border-radius: 18px;
                color: white;
                font-weight: bold;
                font-size: 16px;
            """)
            ai_avatar.setText("A")
            ai_avatar.setAlignment(Qt.AlignCenter)
            h_layout.addWidget(ai_avatar)
            h_layout.setAlignment(ai_avatar, Qt.AlignTop)  # 头像靠上对齐
        
        # 添加消息容器到聊天内容布局
        self.chat_content_layout.addWidget(container)
        
        # 强制刷新布局确保显示最新消息
        QApplication.processEvents()
        
        # 滚动到最新消息
        self._scroll_to_bottom()
        
        logger.info(f"成功添加消息气泡")
        
    def _scroll_to_bottom(self):
        """将聊天滚动区域滚动到最底部显示最新消息"""
        try:
            # 使用QTimer延迟执行滚动操作，确保在UI完全更新后再执行
            from PySide6.QtCore import QTimer
            
            def perform_scroll():
                if hasattr(self, 'ui') and hasattr(self.ui, 'chat_test_scroll'):
                    # 获取滚动区域和其垂直滚动条
                    scroll_bar = self.ui.chat_test_scroll.verticalScrollBar()
                    if scroll_bar:
                        # 强制再刷新一次以确保所有元素都已经加载
                        QApplication.processEvents()
                        # 将滚动条设置到最大值（即滚动到底部）
                        scroll_bar.setValue(scroll_bar.maximum())
                        logger.info("滚动到最新消息 - 延迟执行")
                    else:
                        logger.warning("无法获取垂直滚动条")
            
            # 使用singleShot延迟100毫秒执行滚动操作，给界面足够的时间更新
            QTimer.singleShot(100, perform_scroll)
            
            # 再次延迟执行一次，确保在复杂情况下也能滚动到底部
            QTimer.singleShot(300, perform_scroll)
            
        except Exception as e:
            logger.error(f"滚动到底部时出错: {str(e)}")
    
    def _create_message_bubble(self, message, bg_color, text_color, width):
        """创建消息气泡组件"""
        # 创建气泡框架
        bubble = QFrame()
        bubble.setStyleSheet(f"""
            QFrame {{
                background-color: {bg_color};
                border-radius: 15px;
            }}
        """)
        
        # 气泡内布局
        bubble_layout = QVBoxLayout(bubble)
        bubble_layout.setContentsMargins(12, 10, 12, 10)
        
        # 使用QTextEdit显示消息 - 支持更好的自动换行
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setText(message)
        text_edit.setStyleSheet(f"""
            QTextEdit {{
                background-color: transparent;
                color: {text_color};
                border: none;
                font-family: Arial, sans-serif;
                font-size: 14px;
                line-height: 1.4;
            }}
        """)
        
        # 设置文本编辑器的宽度
        content_width = width - 30  # 减去内边距和滚动条空间
        text_edit.setFixedWidth(content_width)
        
        # 计算并设置合适的高度
        doc = text_edit.document()
        doc.setTextWidth(content_width)
        
        # 根据文本内容计算行数和所需高度
        # 根据文档实际高度做自适应
        doc_height = doc.size().height()
        
        # 添加额外空间确保文本完全显示
        # 文本越长添加的空间越大
        padding = min(20, max(15, int(doc_height * 0.05)))
        text_edit.setFixedHeight(doc_height + padding)
        
        # 移除滚动条
        text_edit.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        text_edit.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # 添加到气泡布局
        bubble_layout.addWidget(text_edit)
        
        return bubble
        
    def delete_chat_history(self):
        """删除当前店铺的测试聊天记录"""
        try:
            if not self.current_store:
                logger.warning("未选择店铺，无法删除聊天记录")
                return
            
            # 构建测试聊天ID
            test_chat_id = f"{TEST_CHAT_PREFIX}{self.current_store}"
            
            # 创建确认对话框
            from PySide6.QtWidgets import QMessageBox
            confirm = QMessageBox()
            confirm.setWindowTitle("确认删除")
            confirm.setText(f"您确定要删除店铺 '{self.current_store}' 的所有测试聊天记录吗？")
            confirm.setIcon(QMessageBox.Warning)
            confirm.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            confirm.setDefaultButton(QMessageBox.No)
            
            # 如果用户确认，执行删除操作
            if confirm.exec() == QMessageBox.Yes:
                # 调用数据库操作执行删除
                result = self.context_ops.delete_context(
                    user_name="current_user",
                    store_name=self.current_store,
                    chat_id=test_chat_id
                )
                
                if result:
                    logger.info(f"成功删除店铺 '{self.current_store}' 的测试聊天记录")
                    
                    # 清空当前聊天内容
                    self.clear_chat_container()
                    
                    self.chat_content_layout.addWidget(delete_fail_label)
                    
                    # 立即刷新并滚动到底部
                    QApplication.processEvents()
                    self._scroll_to_bottom()
            
        except Exception as e:
            logger.error(f"删除聊天记录时出错: {str(e)}")
    
    def refresh(self):
        """刷新聊天测试页面"""
        try:
            logger.info("开始刷新聊天测试页面")
            
            # 清空聊天容器
            self.clear_chat_container()
            
            # 请求一下屏幕刷新
            QApplication.processEvents()
            
            # 加载店铺列表
            self.load_stores()
            
            # 确保我们有内容布局
            if not self.chat_content_layout:
                logger.warning("内容布局引用不存在，尝试重新创建")
                self.clear_chat_container()  # 重试初始化
            
            # 显示欢迎消息
            welcome_label = QLabel("请选择店铺并在下方输入框中发送消息进行聊天测试")
            welcome_label.setObjectName("chat_test_welcome")
            welcome_label.setAlignment(Qt.AlignCenter)
            welcome_label.setWordWrap(True)
            welcome_label.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 14px;
                    padding: 20px;
                    background-color: #2C313C;
                    border-radius: 5px;
                }
            """)
            
            # 添加欢迎消息到内容区域
            if self.chat_content_layout:
                self.chat_content_layout.addWidget(welcome_label)
                logger.info("添加了欢迎信息到内容区域")
                
                # 立即刷新并滚动到底部
                QApplication.processEvents()
                self._scroll_to_bottom()
            else:
                logger.error("无法添加欢迎信息: 内容布局不存在")
            
            logger.info("成功刷新聊天测试页面")
            
        except Exception as e:
            logger.error(f"刷新聊天测试页面出错: {str(e)}")
