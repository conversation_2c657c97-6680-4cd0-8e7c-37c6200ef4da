# WhatsApp批量消息功能说明

## 功能概述

本功能为WowckerPlugin添加了完整的WhatsApp批量消息发送能力，包括电话号码管理、WhatsApp登录集成、消息发送等核心功能。该功能与现有聊天功能完全隔离，确保系统稳定性。

## 主要特性

### 1. 电话号码管理
- **添加号码模态框**: 支持单个和批量添加电话号码
- **国家代码选择**: 带有国旗图标和搜索功能的下拉框
- **格式化处理**: 自动转换为WhatsApp格式（<EMAIL>）
- **用户友好显示**: 格式化显示为 +XX XXX XXXX XXXX
- **持久化存储**: 号码保存在telenumber数据库表中
- **批量操作**: 支持批量添加和删除

### 2. WhatsApp登录集成
- **QR码登录**: 集成现有的bulk_login.js和bulk_login_manager.py
- **会话持久化**: 登录状态跨页面刷新保持
- **状态管理**: 实时显示登录状态和错误信息
- **自动重连**: 支持会话恢复和状态检查

### 3. 消息发送功能
- **智能按钮控制**: 只有登录成功且有消息内容时才启用发送
- **发送间隔设置**: 可配置发送间隔（1-60秒）
- **进度显示**: 实时显示发送进度和状态
- **错误处理**: 完善的错误处理和用户反馈

### 4. 用户界面
- **现代化设计**: 深色主题，符合应用整体风格
- **响应式布局**: 适配不同屏幕尺寸
- **直观操作**: 清晰的功能分区和操作流程

## 技术架构

### 核心组件

1. **PhoneNumberDialog** (`gui/widgets/phone_number_dialog.py`)
   - 电话号码添加对话框
   - 支持国家代码选择和批量输入

2. **WhatsAppLoginWidget** (`gui/widgets/whatsapp_login_widget.py`)
   - WhatsApp登录界面组件
   - QR码显示和状态管理

3. **BulkMessageWidget** (`gui/widgets/bulk_message_widget.py`)
   - 消息输入和发送界面
   - 发送状态控制和进度显示

4. **WhatsAppBulkController** (`gui/core/whatsapp_bulk_controller.py`)
   - 主控制器，整合所有功能
   - 处理UI事件和业务逻辑

5. **BulkMessageService** (`services/bulk_message_service.py`)
   - 业务逻辑服务层
   - 整合登录、号码管理、消息发送

### 工具类

1. **PhoneNumberUtils** (`utils/phone_number_utils.py`)
   - 电话号码格式化和验证
   - 支持多国号码格式

2. **LoginStateManager** (`utils/login_state_manager.py`)
   - 登录状态持久化管理
   - 跨会话状态保持

## 使用说明

### 1. 添加电话号码
1. 点击"添加号码"按钮
2. 在弹出的对话框中选择国家/地区
3. 输入电话号码（支持多种格式）
4. 点击"添加此号码"或使用批量添加功能

### 2. WhatsApp登录
1. 点击"开始登录"按钮
2. 扫描显示的QR码
3. 等待登录完成确认

### 3. 发送批量消息
1. 确保已登录WhatsApp
2. 在消息输入框中输入要发送的内容
3. 设置发送间隔（建议3-5秒）
4. 点击"发送批量消息"按钮

## 数据库结构

使用现有的`telenumber`表存储电话号码：
- `phone_number`: WhatsApp格式的电话号码
- `username`: 用户名
- `store_name`: 店铺名称
- `created_at`: 创建时间
- `status`: 状态信息

## 配置要求

### 依赖包
- PySide6: GUI框架
- qrcode: QR码生成（可选）
- 现有的WhatsApp Web.js相关依赖

### 文件结构
```
WowckerPlugin/
├── gui/
│   ├── widgets/
│   │   ├── phone_number_dialog.py
│   │   ├── whatsapp_login_widget.py
│   │   └── bulk_message_widget.py
│   └── core/
│       └── whatsapp_bulk_controller.py
├── services/
│   └── bulk_message_service.py
├── utils/
│   ├── phone_number_utils.py
│   └── login_state_manager.py
└── data/
    └── login_states/  # 登录状态存储目录
```

## 安全考虑

1. **数据隔离**: 批量消息功能与现有聊天功能完全隔离
2. **状态管理**: 登录状态安全存储，支持过期清理
3. **错误处理**: 完善的异常处理，避免系统崩溃
4. **用户确认**: 发送前需要用户确认，防止误操作

## 测试

运行测试脚本验证功能：
```bash
python test_whatsapp_bulk_feature.py
```

测试覆盖：
- 电话号码工具类
- 登录状态管理器
- 数据库操作
- 批量消息服务
- UI组件创建

## 故障排除

### 常见问题

1. **登录失败**
   - 检查网络连接
   - 确保WhatsApp Web服务正常
   - 重新扫描QR码

2. **号码添加失败**
   - 检查号码格式是否正确
   - 确保数据库连接正常
   - 检查是否重复添加

3. **发送失败**
   - 确保已登录WhatsApp
   - 检查号码列表是否为空
   - 验证消息内容是否有效

### 日志查看
- 应用日志：查看控制台输出
- 登录状态：检查 `data/login_states/` 目录
- 数据库状态：查询 `telenumber` 表

## 更新历史

### v1.0.0 (2024-12-21)
- 初始版本发布
- 完整的电话号码管理功能
- WhatsApp登录集成
- 批量消息发送功能
- 现代化用户界面

## 技术支持

如遇到问题，请检查：
1. 依赖包是否正确安装
2. 数据库连接是否正常
3. WhatsApp Web服务是否可用
4. 网络连接是否稳定

## 开发说明

### 扩展功能
- 可添加消息模板功能
- 支持发送图片和文件
- 添加发送统计和报告
- 集成更多国家的号码格式

### 代码贡献
- 遵循现有代码风格
- 添加适当的错误处理
- 编写测试用例
- 更新文档
