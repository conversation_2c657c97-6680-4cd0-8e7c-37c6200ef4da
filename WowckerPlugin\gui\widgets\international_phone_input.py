"""
国际电话号码输入组件
支持国家代码选择和本地号码输入
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QComboBox, 
                               QLineEdit, QLabel, QPushButton, QCompleter, 
                               QListWidget, QListWidgetItem, QFrame)
from PySide6.QtCore import Qt, Signal, QStringListModel
from PySide6.QtGui import QFont

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from gui.core.country_code_manager import CountryCodeManager

class CountryCodeComboBox(QComboBox):
    """自定义国家代码下拉框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.country_manager = CountryCodeManager()
        self.setEditable(True)
        self.setInsertPolicy(QComboBox.NoInsert)
        
        # 设置样式
        self.setStyleSheet("""
            QComboBox {
                border: 2px solid #3c4454;
                border-radius: 5px;
                padding: 8px;
                background-color: #21252d;
                color: #c3ccdf;
                min-width: 200px;
                font-size: 12px;
                min-height: 20px;
            }
            QComboBox:hover {
                border-color: #4A90E2;
            }
            QComboBox:focus {
                border-color: #357ABD;
                background-color: #2a2f3a;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #c3ccdf;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
                margin-right: 8px;
            }
            QComboBox QAbstractItemView {
                background-color: #21252d;
                color: #c3ccdf;
                border: 1px solid #3c4454;
                selection-background-color: #4A90E2;
            }
        """)
        
        self.populate_countries()
        self.setup_completer()
        
        # 连接信号
        self.lineEdit().textChanged.connect(self.on_text_changed)
        self.currentTextChanged.connect(self.on_selection_changed)
    
    def populate_countries(self):
        """填充国家列表"""
        self.clear()
        
        # 添加常用国家
        common_countries = self.country_manager.get_common_countries()
        if common_countries:
            for code, info in common_countries:
                display_text = f"{info['flag']} {info['name_cn']} {info['code']}"
                self.addItem(display_text, code)
            
            # 添加分隔符
            self.insertSeparator(len(common_countries))
        
        # 添加其他国家
        all_countries = self.country_manager.get_all_countries()
        other_countries = [item for item in all_countries if not item[1].get("common", False)]
        
        for code, info in other_countries:
            display_text = f"{info['flag']} {info['name_cn']} {info['code']}"
            self.addItem(display_text, code)
        
        # 默认选择中国
        self.set_country_code("86")
    
    def setup_completer(self):
        """设置自动完成"""
        country_names = []
        for code, info in self.country_manager.get_all_countries():
            country_names.extend([
                info['name_cn'],
                info['name_en'],
                info['code'],
                f"{info['name_cn']} {info['code']}",
                f"{info['name_en']} {info['code']}"
            ])
        
        completer = QCompleter(country_names, self)
        completer.setCaseSensitivity(Qt.CaseInsensitive)
        completer.setFilterMode(Qt.MatchContains)
        self.setCompleter(completer)
    
    def on_text_changed(self, text):
        """处理文本变化"""
        # 搜索匹配的国家
        results = self.country_manager.search_countries(text)
        if results:
            # 更新下拉列表
            self.clear()
            for code, info in results[:10]:  # 限制显示数量
                display_text = f"{info['flag']} {info['name_cn']} {info['code']}"
                self.addItem(display_text, code)
    
    def on_selection_changed(self, text):
        """处理选择变化"""
        pass
    
    def get_selected_country_code(self):
        """获取选中的国家代码"""
        current_data = self.currentData()
        if current_data:
            return current_data
        
        # 如果没有选中项，尝试从文本解析
        current_text = self.currentText()
        for code, info in self.country_manager.get_all_countries():
            if (info['code'] in current_text or 
                info['name_cn'] in current_text or 
                info['name_en'] in current_text):
                return code
        
        return "86"  # 默认返回中国
    
    def set_country_code(self, country_code):
        """设置国家代码"""
        for i in range(self.count()):
            if self.itemData(i) == country_code:
                self.setCurrentIndex(i)
                break
    
    def get_country_info(self):
        """获取当前选中国家的信息"""
        country_code = self.get_selected_country_code()
        return self.country_manager.get_country_info(country_code)

class InternationalPhoneInput(QWidget):
    """国际电话号码输入组件"""
    
    # 信号
    phoneChanged = Signal(str, str)  # 国家代码, 本地号码
    validationChanged = Signal(bool, str)  # 是否有效, 错误信息
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.country_manager = CountryCodeManager()
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)

        # 输入区域布局
        input_layout = QHBoxLayout()
        input_layout.setContentsMargins(0, 0, 0, 0)
        input_layout.setSpacing(5)

        # 国家代码选择框
        self.country_combo = CountryCodeComboBox()
        self.country_combo.setMinimumWidth(200)
        self.country_combo.setMaximumWidth(250)
        input_layout.addWidget(self.country_combo)

        # 本地号码输入框
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("输入本地电话号码")
        self.phone_input.setMinimumHeight(35)
        self.phone_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #3c4454;
                border-radius: 5px;
                padding: 8px;
                background-color: #21252d;
                color: #c3ccdf;
                font-size: 12px;
                min-width: 150px;
            }
            QLineEdit:hover {
                border-color: #4A90E2;
            }
            QLineEdit:focus {
                border-color: #357ABD;
                background-color: #2a2f3a;
            }
            QLineEdit::placeholder {
                color: #666;
            }
        """)
        input_layout.addWidget(self.phone_input, 1)

        # 检测按钮
        self.detect_btn = QPushButton("🔍")
        self.detect_btn.setToolTip("自动检测完整号码格式")
        self.detect_btn.setFixedSize(35, 35)
        self.detect_btn.setStyleSheet("""
            QPushButton {
                border: 2px solid #4CAF50;
                border-radius: 5px;
                background-color: #4CAF50;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #66BB6A;
            }
            QPushButton:pressed {
                background-color: #388E3C;
            }
        """)
        input_layout.addWidget(self.detect_btn)

        # 添加输入布局到主布局
        main_layout.addLayout(input_layout)

        # 状态标签
        self.status_label = QLabel()
        self.status_label.setStyleSheet("color: #c3ccdf; font-size: 10px; padding: 2px;")
        self.status_label.setWordWrap(True)
        main_layout.addWidget(self.status_label)
    
    def connect_signals(self):
        """连接信号"""
        self.country_combo.currentTextChanged.connect(self.on_input_changed)
        self.phone_input.textChanged.connect(self.on_input_changed)
        self.detect_btn.clicked.connect(self.auto_detect_format)
    
    def on_input_changed(self):
        """处理输入变化"""
        country_code = self.country_combo.get_selected_country_code()
        local_number = self.phone_input.text().strip()
        
        if not local_number:
            self.status_label.setText("")
            self.validationChanged.emit(False, "")
            return
        
        # 验证号码
        is_valid, result = self.country_manager.validate_phone_number(country_code, local_number)
        
        if is_valid:
            # 显示格式化后的号码
            display_format = self.country_manager.format_for_display(country_code, result)
            self.status_label.setText(f"✅ 格式正确: {display_format}")
            self.status_label.setStyleSheet("color: #4CAF50; font-size: 10px;")
            self.validationChanged.emit(True, "")
            self.phoneChanged.emit(country_code, result)
        else:
            self.status_label.setText(f"❌ {result}")
            self.status_label.setStyleSheet("color: #f44336; font-size: 10px;")
            self.validationChanged.emit(False, result)
    
    def auto_detect_format(self):
        """自动检测号码格式"""
        full_number = self.phone_input.text().strip()
        if not full_number:
            return
        
        # 尝试解析完整号码
        parsed = self.country_manager.parse_international_number(full_number)
        if parsed:
            country_code, local_number = parsed
            self.country_combo.set_country_code(country_code)
            self.phone_input.setText(local_number)
            self.status_label.setText("🔍 已自动检测号码格式")
            self.status_label.setStyleSheet("color: #2196F3; font-size: 10px;")
        else:
            self.status_label.setText("❌ 无法识别号码格式")
            self.status_label.setStyleSheet("color: #f44336; font-size: 10px;")
    
    def get_phone_data(self):
        """获取电话号码数据"""
        country_code = self.country_combo.get_selected_country_code()
        local_number = self.phone_input.text().strip()
        
        if not local_number:
            return None
        
        is_valid, cleaned_number = self.country_manager.validate_phone_number(country_code, local_number)
        if not is_valid:
            return None
        
        return {
            "country_code": country_code,
            "local_number": cleaned_number,
            "whatsapp_format": self.country_manager.format_for_whatsapp(country_code, cleaned_number),
            "display_format": self.country_manager.format_for_display(country_code, cleaned_number),
            "country_info": self.country_manager.get_country_info(country_code)
        }
    
    def clear(self):
        """清空输入"""
        self.phone_input.clear()
        self.status_label.setText("")
        self.country_combo.set_country_code("86")  # 重置为中国
    
    def set_phone_number(self, whatsapp_format):
        """设置电话号码（从WhatsApp格式解析）"""
        if whatsapp_format.endswith('@c.us'):
            phone_number = whatsapp_format[:-5]
            parsed = self.country_manager.parse_international_number(phone_number)
            if parsed:
                country_code, local_number = parsed
                self.country_combo.set_country_code(country_code)
                self.phone_input.setText(local_number)
