# -*- coding: utf-8 -*-

"""
电话号码格式化和处理工具
"""

import re
from typing import Tuple, Optional, Dict, Any

class PhoneNumberUtils:
    """电话号码处理工具类"""
    
    # 国家代码映射
    COUNTRY_CODES = {
        "86": {"name": "中国", "flag": "🇨🇳", "format": "+86 {0} {1} {2}"},
        "1": {"name": "美国/加拿大", "flag": "🇺🇸", "format": "+1 ({0}) {1}-{2}"},
        "44": {"name": "英国", "flag": "🇬🇧", "format": "+44 {0} {1} {2}"},
        "81": {"name": "日本", "flag": "🇯🇵", "format": "+81 {0}-{1}-{2}"},
        "82": {"name": "韩国", "flag": "🇰🇷", "format": "+82 {0}-{1}-{2}"},
        "49": {"name": "德国", "flag": "🇩🇪", "format": "+49 {0} {1} {2}"},
        "33": {"name": "法国", "flag": "🇫🇷", "format": "+33 {0} {1} {2} {3}"},
        "61": {"name": "澳大利亚", "flag": "🇦🇺", "format": "+61 {0} {1} {2}"},
        "65": {"name": "新加坡", "flag": "🇸🇬", "format": "+65 {0} {1}"},
        "91": {"name": "印度", "flag": "🇮🇳", "format": "+91 {0} {1} {2}"},
    }
    
    @staticmethod
    def clean_phone_number(phone_number: str) -> str:
        """
        清理电话号码，只保留数字和+号
        
        Args:
            phone_number: 原始电话号码
            
        Returns:
            str: 清理后的电话号码
        """
        if not phone_number:
            return ""
        
        # 移除所有非数字和非+号的字符
        cleaned = re.sub(r'[^\d+]', '', phone_number.strip())
        return cleaned
    
    @staticmethod
    def parse_phone_number(phone_number: str) -> Tuple[Optional[str], Optional[str]]:
        """
        解析电话号码，提取国家代码和本地号码
        
        Args:
            phone_number: 电话号码
            
        Returns:
            Tuple[Optional[str], Optional[str]]: (国家代码, 本地号码)
        """
        cleaned = PhoneNumberUtils.clean_phone_number(phone_number)
        
        if not cleaned:
            return None, None
        
        # 移除开头的+号
        if cleaned.startswith('+'):
            cleaned = cleaned[1:]
        
        # 尝试匹配已知的国家代码
        for code in sorted(PhoneNumberUtils.COUNTRY_CODES.keys(), key=len, reverse=True):
            if cleaned.startswith(code):
                country_code = code
                local_number = cleaned[len(code):]
                return country_code, local_number
        
        # 如果没有匹配到国家代码，假设是中国号码（11位）
        if len(cleaned) == 11 and cleaned.startswith(('13', '14', '15', '16', '17', '18', '19')):
            return "86", cleaned
        
        # 如果是10位数字，假设是美国号码
        if len(cleaned) == 10:
            return "1", cleaned
        
        return None, None
    
    @staticmethod
    def format_for_display(phone_number: str) -> str:
        """
        格式化电话号码用于显示
        
        Args:
            phone_number: 电话号码
            
        Returns:
            str: 格式化后的显示号码
        """
        country_code, local_number = PhoneNumberUtils.parse_phone_number(phone_number)
        
        if not country_code or not local_number:
            return phone_number  # 返回原始号码
        
        # 根据国家代码格式化
        if country_code == "86" and len(local_number) == 11:
            # 中国手机号码: +86 138 1234 5678
            return f"+86 {local_number[:3]} {local_number[3:7]} {local_number[7:]}"
        elif country_code == "1" and len(local_number) == 10:
            # 美国/加拿大号码: +****************
            return f"+1 ({local_number[:3]}) {local_number[3:6]}-{local_number[6:]}"
        elif country_code == "44":
            # 英国号码
            if len(local_number) == 10:
                return f"+44 {local_number[:4]} {local_number[4:7]} {local_number[7:]}"
            else:
                return f"+44 {local_number}"
        elif country_code == "81":
            # 日本号码
            if len(local_number) >= 10:
                return f"+81 {local_number[:2]}-{local_number[2:6]}-{local_number[6:]}"
            else:
                return f"+81 {local_number}"
        else:
            # 其他国家，使用通用格式
            return f"+{country_code} {local_number}"
    
    @staticmethod
    def format_for_whatsapp(phone_number: str) -> Optional[str]:
        """
        格式化电话号码用于WhatsApp
        
        Args:
            phone_number: 电话号码
            
        Returns:
            Optional[str]: WhatsApp格式的号码 (<EMAIL>)
        """
        country_code, local_number = PhoneNumberUtils.parse_phone_number(phone_number)
        
        if not country_code or not local_number:
            return None
        
        return f"{country_code}{local_number}@c.us"
    
    @staticmethod
    def validate_phone_number(phone_number: str, country_code: str = None) -> Tuple[bool, str]:
        """
        验证电话号码格式
        
        Args:
            phone_number: 电话号码
            country_code: 指定的国家代码（可选）
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息或成功信息)
        """
        if not phone_number:
            return False, "电话号码不能为空"
        
        cleaned = PhoneNumberUtils.clean_phone_number(phone_number)
        
        if not cleaned:
            return False, "电话号码格式不正确"
        
        # 解析号码
        parsed_country, local_number = PhoneNumberUtils.parse_phone_number(cleaned)
        
        if not parsed_country or not local_number:
            return False, "无法识别的电话号码格式"
        
        # 如果指定了国家代码，检查是否匹配
        if country_code and parsed_country != country_code:
            return False, f"电话号码与指定的国家代码不匹配"
        
        # 根据国家代码验证号码长度
        if parsed_country == "86":
            # 中国手机号码应该是11位
            if len(local_number) != 11:
                return False, "中国手机号码应为11位"
            if not local_number.startswith(('13', '14', '15', '16', '17', '18', '19')):
                return False, "请输入有效的中国手机号码"
        elif parsed_country == "1":
            # 美国/加拿大号码应该是10位
            if len(local_number) != 10:
                return False, "美国/加拿大电话号码应为10位"
        elif parsed_country in ["44", "81", "82", "49", "33", "61", "65", "91"]:
            # 其他国家的基本长度检查
            if len(local_number) < 7 or len(local_number) > 15:
                return False, "电话号码长度不正确"
        
        return True, "电话号码格式正确"
    
    @staticmethod
    def get_country_info(country_code: str) -> Optional[Dict[str, Any]]:
        """
        获取国家信息
        
        Args:
            country_code: 国家代码
            
        Returns:
            Optional[Dict[str, Any]]: 国家信息
        """
        return PhoneNumberUtils.COUNTRY_CODES.get(country_code)
    
    @staticmethod
    def extract_country_code_from_whatsapp_format(whatsapp_number: str) -> Optional[str]:
        """
        从WhatsApp格式的号码中提取国家代码
        
        Args:
            whatsapp_number: WhatsApp格式号码 (如: <EMAIL>)
            
        Returns:
            Optional[str]: 国家代码
        """
        if not whatsapp_number or not whatsapp_number.endswith('@c.us'):
            return None
        
        # 移除@c.us后缀
        number_part = whatsapp_number[:-5]
        
        # 尝试匹配国家代码
        for code in sorted(PhoneNumberUtils.COUNTRY_CODES.keys(), key=len, reverse=True):
            if number_part.startswith(code):
                return code
        
        return None
    
    @staticmethod
    def convert_whatsapp_to_display(whatsapp_number: str) -> str:
        """
        将WhatsApp格式的号码转换为显示格式
        
        Args:
            whatsapp_number: WhatsApp格式号码
            
        Returns:
            str: 显示格式号码
        """
        if not whatsapp_number or not whatsapp_number.endswith('@c.us'):
            return whatsapp_number
        
        # 移除@c.us后缀
        number_part = whatsapp_number[:-5]
        
        # 添加+号并格式化
        return PhoneNumberUtils.format_for_display(f"+{number_part}")
