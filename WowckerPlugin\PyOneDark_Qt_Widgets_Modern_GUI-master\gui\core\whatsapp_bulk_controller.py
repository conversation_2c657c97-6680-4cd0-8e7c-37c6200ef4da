# -*- coding: utf-8 -*-

"""
WhatsApp批量消息控制器
整合电话号码管理、登录、消息发送等功能
"""

import os
import sys
import logging
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QMessageBox, QTableWidgetItem, QPushButton, QHBoxLayout, QWidget

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

try:
    from services.bulk_message_service import BulkMessageService
    from gui.widgets.phone_number_dialog import PhoneNumberDialog
    from gui.widgets.whatsapp_login_widget import WhatsAppLoginWidget
    from gui.widgets.bulk_message_widget import BulkMessageWidget
    from utils.phone_number_utils import PhoneNumberUtils
except ImportError as e:
    logging.error(f"导入模块失败: {e}")

logger = logging.getLogger(__name__)

class WhatsAppBulkController(QObject):
    """WhatsApp批量消息控制器"""
    
    # 信号
    status_updated = Signal(str)  # 状态更新
    
    def __init__(self, main_window, username: str = "default_user", store_name: str = "bulk_messaging"):
        super().__init__()
        self.main_window = main_window
        self.username = username
        self.store_name = store_name
        
        # 初始化服务
        self.bulk_service = BulkMessageService(username, store_name)
        
        # UI组件引用
        self.phone_numbers_table = None
        self.login_widget = None
        self.message_widget = None
        self.phone_dialog = None
        
        # 状态
        self.is_logged_in = False
        self.is_sending = False
        
        # 设置服务回调
        self._setup_service_callbacks()
        
        # 定时器用于更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(5000)  # 每5秒更新一次
        
        logger.info(f"初始化WhatsApp批量消息控制器: {username}@{store_name}")
    
    def _setup_service_callbacks(self):
        """设置服务回调"""
        self.bulk_service.add_login_status_callback(self._on_login_status_changed)
        self.bulk_service.add_qr_code_callback(self._on_qr_code_received)
        self.bulk_service.add_send_progress_callback(self._on_send_progress)
        self.bulk_service.add_send_completion_callback(self._on_send_completion)
        self.bulk_service.add_error_callback(self._on_error)
    
    def setup_ui_components(self, page_widget):
        """设置UI组件"""
        try:
            # 获取UI组件引用
            self.phone_numbers_table = getattr(page_widget, 'phone_numbers_table', None)
            
            # 创建登录组件
            login_container = getattr(page_widget, 'login_container', None)
            if login_container:
                self.login_widget = WhatsAppLoginWidget(self.username, self.store_name)
                login_container.layout().addWidget(self.login_widget)
                
                # 连接登录状态信号
                self.login_widget.login_status_changed.connect(self._on_login_status_changed)
                self.login_widget.login_error.connect(self._on_error)
            
            # 创建消息发送组件
            message_container = getattr(page_widget, 'message_container', None)
            if message_container:
                self.message_widget = BulkMessageWidget()
                message_container.layout().addWidget(self.message_widget)
                
                # 连接消息发送信号
                self.message_widget.send_message_requested.connect(self._on_send_message_requested)
            
            # 设置按钮事件
            add_phone_btn = getattr(page_widget, 'add_phone_btn', None)
            if add_phone_btn:
                add_phone_btn.clicked.connect(self.show_add_phone_dialog)
            
            # 初始化数据
            self._load_phone_numbers()
            self._update_login_status()
            
            logger.info("UI组件设置完成")
            
        except Exception as e:
            logger.error(f"设置UI组件失败: {e}")
    
    def show_add_phone_dialog(self):
        """显示添加电话号码对话框"""
        try:
            if not self.phone_dialog:
                self.phone_dialog = PhoneNumberDialog(self.main_window)
                self.phone_dialog.phone_added.connect(self._on_phone_added)
            
            self.phone_dialog.show()
            
        except Exception as e:
            logger.error(f"显示电话号码对话框失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"无法打开电话号码对话框: {e}")
    
    def _on_phone_added(self, formatted_number: str, whatsapp_format: str):
        """电话号码添加处理"""
        try:
            success = self.bulk_service.add_phone_number(whatsapp_format, formatted_number)
            if success:
                self._load_phone_numbers()
                self.status_updated.emit(f"成功添加电话号码: {formatted_number}")
            else:
                QMessageBox.warning(self.main_window, "警告", "电话号码已存在或添加失败")
                
        except Exception as e:
            logger.error(f"添加电话号码失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"添加电话号码失败: {e}")
    
    def _load_phone_numbers(self):
        """加载电话号码到表格"""
        if not self.phone_numbers_table:
            return
            
        try:
            numbers = self.bulk_service.get_phone_numbers()
            
            self.phone_numbers_table.setRowCount(len(numbers))
            
            for row, number in enumerate(numbers):
                # 序号
                self.phone_numbers_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))
                
                # 显示格式的电话号码
                display_format = number.get('display_format', number.get('phone_number', ''))
                self.phone_numbers_table.setItem(row, 1, QTableWidgetItem(display_format))
                
                # 添加时间
                created_at = number.get('created_at', '')
                self.phone_numbers_table.setItem(row, 2, QTableWidgetItem(created_at))
                
                # 状态
                status = number.get('status', 'pending')
                self.phone_numbers_table.setItem(row, 3, QTableWidgetItem(status))
                
                # 操作按钮
                delete_btn = QPushButton("删除")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 5px 10px;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, phone=number['phone_number']: self._delete_phone_number(phone))
                
                button_widget = QWidget()
                button_layout = QHBoxLayout(button_widget)
                button_layout.addWidget(delete_btn)
                button_layout.setContentsMargins(5, 5, 5, 5)
                
                self.phone_numbers_table.setCellWidget(row, 4, button_widget)
            
            logger.info(f"加载了 {len(numbers)} 个电话号码")
            
        except Exception as e:
            logger.error(f"加载电话号码失败: {e}")
    
    def _delete_phone_number(self, phone_number: str):
        """删除电话号码"""
        try:
            reply = QMessageBox.question(
                self.main_window,
                "确认删除",
                f"确定要删除电话号码 {phone_number} 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success = self.bulk_service.remove_phone_number(phone_number)
                if success:
                    self._load_phone_numbers()
                    self.status_updated.emit(f"成功删除电话号码: {phone_number}")
                else:
                    QMessageBox.warning(self.main_window, "错误", "删除电话号码失败")
                    
        except Exception as e:
            logger.error(f"删除电话号码失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"删除电话号码失败: {e}")
    
    def _on_login_status_changed(self, is_logged_in: bool):
        """登录状态变化处理"""
        self.is_logged_in = is_logged_in
        
        # 更新消息发送组件状态
        if self.message_widget:
            self.message_widget.set_login_status(is_logged_in)
        
        status_text = "已登录" if is_logged_in else "未登录"
        self.status_updated.emit(f"WhatsApp登录状态: {status_text}")
        
        logger.info(f"登录状态变化: {is_logged_in}")
    
    def _on_qr_code_received(self, qr_code: str):
        """QR码接收处理"""
        self.status_updated.emit("请扫描二维码登录WhatsApp")
    
    def _on_send_message_requested(self, message: str, delay_seconds: int):
        """消息发送请求处理"""
        try:
            if not self.is_logged_in:
                QMessageBox.warning(self.main_window, "警告", "请先登录WhatsApp")
                return
            
            numbers = self.bulk_service.get_phone_numbers()
            if not numbers:
                QMessageBox.warning(self.main_window, "警告", "没有电话号码，请先添加电话号码")
                return
            
            # 开始发送
            success = self.bulk_service.send_bulk_message(message, delay_seconds)
            if success:
                self.is_sending = True
                if self.message_widget:
                    self.message_widget.set_sending_status(True, 0, "开始发送...")
                self.status_updated.emit(f"开始向 {len(numbers)} 个号码发送消息")
            else:
                QMessageBox.warning(self.main_window, "错误", "发送消息失败")
                
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"发送消息失败: {e}")
    
    def _on_send_progress(self, progress: int, status: str, current_phone: str):
        """发送进度处理"""
        if self.message_widget:
            self.message_widget.set_sending_status(True, progress, status)
        
        display_phone = PhoneNumberUtils.convert_whatsapp_to_display(current_phone)
        self.status_updated.emit(f"发送进度: {progress}% - {display_phone}")
    
    def _on_send_completion(self, success: bool, sent_count: int, total_count: int, errors: list):
        """发送完成处理"""
        self.is_sending = False
        
        if self.message_widget:
            self.message_widget.set_sending_status(False)
        
        if success:
            self.status_updated.emit(f"发送完成: 成功 {sent_count}/{total_count}")
            QMessageBox.information(
                self.main_window,
                "发送完成",
                f"批量消息发送完成！\n\n成功: {sent_count}\n总数: {total_count}\n失败: {len(errors)}"
            )
        else:
            self.status_updated.emit(f"发送失败: {sent_count}/{total_count}")
            QMessageBox.warning(self.main_window, "发送失败", "批量消息发送失败，请检查网络连接和登录状态")
    
    def _on_error(self, error_message: str):
        """错误处理"""
        self.status_updated.emit(f"错误: {error_message}")
        logger.error(f"WhatsApp批量消息错误: {error_message}")
    
    def _update_status(self):
        """定时更新状态"""
        try:
            # 检查登录状态
            current_login_status = self.bulk_service.is_logged_in()
            if current_login_status != self.is_logged_in:
                self._on_login_status_changed(current_login_status)
            
            # 检查发送状态
            current_sending_status = self.bulk_service.is_sending_in_progress()
            if current_sending_status != self.is_sending:
                self.is_sending = current_sending_status
                if self.message_widget:
                    self.message_widget.set_sending_status(current_sending_status)
            
        except Exception as e:
            logger.error(f"更新状态失败: {e}")
    
    def _update_login_status(self):
        """更新登录状态"""
        try:
            is_logged_in = self.bulk_service.is_logged_in()
            self._on_login_status_changed(is_logged_in)
        except Exception as e:
            logger.error(f"更新登录状态失败: {e}")
    
    def get_session_info(self) -> dict:
        """获取会话信息"""
        return self.bulk_service.get_session_info()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.status_timer:
                self.status_timer.stop()
            
            if self.bulk_service:
                # 这里可以添加服务清理逻辑
                pass
                
            logger.info("WhatsApp批量消息控制器清理完成")
            
        except Exception as e:
            logger.error(f"清理资源失败: {e}")
