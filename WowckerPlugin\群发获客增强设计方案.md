# 群发获客功能增强设计方案

## 1. 需求分析

### 核心需求
1. **WhatsApp登录集成**：使用现有的WhatsApp登录系统
2. **登录优先工作流**：用户必须先登录WhatsApp才能发送消息
3. **数据库增强**：创建telenumber表存储群发电话号码
4. **UI状态管理**：根据登录状态和数据状态控制按钮可用性
5. **多账户兼容**：与现有多账户系统保持一致
6. **功能隔离**：不干扰现有功能

### 技术要求
- 使用wwebjs发送消息
- 通过JSON文件进行进程间通信
- 支持实时进度显示
- 完善的错误处理

## 2. 系统架构设计

### 2.1 数据库设计

#### 新增telenumber表
```sql
CREATE TABLE IF NOT EXISTS telenumber (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone_number TEXT NOT NULL,
    username TEXT NOT NULL,
    store_name TEXT,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (phone_number, username, store_name)
)
```

**字段说明**：
- `phone_number`: 电话号码（格式化后）
- `username`: 用户名（用于多账户隔离）
- `store_name`: 店铺名称（可选，用于店铺级别隔离）
- `status`: 状态（pending/sent/failed/blocked）

### 2.2 WhatsApp登录集成

#### 登录状态管理
```python
class WhatsAppLoginManager:
    def __init__(self, username, store_name=None):
        self.username = username
        self.store_name = store_name or "bulk_messaging"
        self.session_dir = self._get_session_dir()
        self.is_logged_in = False
        self.login_process = None
    
    def _get_session_dir(self):
        # 使用与现有系统一致的目录结构
        safe_username = self._sanitize_name(self.username)
        safe_store = self._sanitize_name(self.store_name)
        return f"{safe_username}_{safe_store}"
```

#### 登录流程
1. 用户点击"登录WhatsApp"按钮
2. 启动独立的Node.js进程（类似现有聊天服务）
3. 显示QR码供用户扫描
4. 登录成功后更新UI状态
5. 启用群发功能

### 2.3 消息发送架构

#### 发送流程设计
```
用户界面 → 群发控制器 → WhatsApp发送服务 → Node.js进程 → WhatsApp Web
    ↓           ↓              ↓              ↓           ↓
数据库更新 ← 状态回调 ← JSON通信文件 ← 发送结果 ← 消息发送状态
```

#### 进程间通信
- **发送请求文件**: `bulk_send_requests.json`
- **发送状态文件**: `bulk_send_status.json`
- **登录状态文件**: `login_status.json`

## 3. UI设计增强

### 3.1 新增UI组件

#### 登录区域
```
┌─────────────────────────────────────┐
│ WhatsApp登录状态                     │
│ ○ 未登录  [登录WhatsApp] [登录状态]   │
│ ● 已登录  [退出登录]   [重新登录]     │
└─────────────────────────────────────┘
```

#### 状态指示器
- **登录状态灯**：红色（未登录）/ 绿色（已登录）
- **发送状态栏**：显示当前发送进度
- **按钮状态管理**：根据条件启用/禁用

### 3.2 按钮状态逻辑

```python
def update_ui_state(self):
    # 登录状态检查
    is_logged_in = self.login_manager.is_logged_in
    
    # 数据状态检查
    has_numbers = len(self.phone_numbers) > 0
    has_message = len(self.message_input_field.toPlainText().strip()) > 0
    
    # 更新按钮状态
    self.login_btn.setEnabled(not is_logged_in)
    self.logout_btn.setEnabled(is_logged_in)
    self.add_phone_btn.setEnabled(True)  # 始终可用
    self.start_bulk_send_btn.setEnabled(
        is_logged_in and has_numbers and has_message
    )
```

## 4. 核心组件实现

### 4.1 数据库操作类

```python
class TeleNumberOperations:
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def add_phone_number(self, phone_number, username, store_name=None):
        """添加电话号码到数据库"""
        
    def get_phone_numbers(self, username, store_name=None):
        """获取用户的电话号码列表"""
        
    def update_phone_status(self, phone_id, status):
        """更新电话号码状态"""
        
    def delete_phone_number(self, phone_id):
        """删除电话号码"""
```

### 4.2 WhatsApp登录管理器

```python
class WhatsAppBulkLoginManager:
    def __init__(self, username, store_name="bulk_messaging"):
        self.username = username
        self.store_name = store_name
        self.session_dir = self._get_session_dir()
        self.login_process = None
        self.is_logged_in = False
        
    def start_login(self):
        """启动登录流程"""
        
    def check_login_status(self):
        """检查登录状态"""
        
    def logout(self):
        """退出登录"""
```

### 4.3 消息发送服务

```python
class WhatsAppBulkSender:
    def __init__(self, login_manager):
        self.login_manager = login_manager
        self.send_process = None
        
    def send_bulk_messages(self, phone_numbers, message, progress_callback):
        """发送群发消息"""
        
    def stop_sending(self):
        """停止发送"""
```

## 5. Node.js脚本设计

### 5.1 群发专用脚本

创建 `bulk_sender.js`：
```javascript
const { Client, LocalAuth } = require('whatsapp-web.js');
const fs = require('fs');
const path = require('path');

// 专门用于群发的WhatsApp客户端
class BulkSenderClient {
    constructor(sessionDir) {
        this.sessionDir = sessionDir;
        this.client = null;
        this.isReady = false;
    }
    
    async initialize() {
        // 初始化客户端
    }
    
    async sendBulkMessages(phoneNumbers, message) {
        // 批量发送消息
    }
}
```

### 5.2 通信协议

#### 发送请求格式
```json
{
    "action": "send_bulk",
    "phone_numbers": ["+8613812345678", "+8613987654321"],
    "message": "您好，这是一条群发消息",
    "delay": 3000
}
```

#### 状态响应格式
```json
{
    "status": "sending",
    "progress": 50,
    "current_phone": "+8613812345678",
    "sent_count": 5,
    "total_count": 10,
    "errors": []
}
```

## 6. 安全和性能考虑

### 6.1 发送限制
- **频率限制**：每条消息间隔3-5秒
- **批量限制**：单次最多50个号码
- **重试机制**：失败自动重试3次
- **黑名单检查**：避免发送到已拉黑号码

### 6.2 数据隔离
- **用户级隔离**：每个用户独立的电话号码列表
- **会话隔离**：独立的WhatsApp会话目录
- **进程隔离**：独立的Node.js进程

### 6.3 错误处理
- **网络异常**：自动重连机制
- **登录失效**：提示重新登录
- **发送失败**：记录失败原因和重试次数
- **进程崩溃**：自动重启机制

## 7. 实施计划

### 阶段1：数据库和基础架构
1. 创建telenumber表
2. 实现数据库操作类
3. 创建登录管理器基础框架

### 阶段2：UI增强
1. 添加登录区域组件
2. 实现状态管理逻辑
3. 更新按钮控制逻辑

### 阶段3：WhatsApp集成
1. 创建群发专用Node.js脚本
2. 实现登录流程
3. 实现消息发送功能

### 阶段4：测试和优化
1. 单元测试
2. 集成测试
3. 性能优化
4. 错误处理完善

## 8. 风险评估

### 技术风险
- **WhatsApp API限制**：可能被检测为自动化行为
- **会话冲突**：与现有聊天服务的会话冲突
- **进程管理**：多进程协调复杂性

### 缓解措施
- **人性化发送**：随机间隔，模拟人工操作
- **独立会话**：使用独立的会话目录
- **进程监控**：完善的进程生命周期管理

## 9. 成功指标

### 功能指标
- ✅ 用户可以成功登录WhatsApp
- ✅ 电话号码可以正确存储和管理
- ✅ 消息可以成功发送到所有号码
- ✅ 发送进度可以实时显示
- ✅ 错误可以正确处理和显示

### 性能指标
- 登录时间 < 30秒
- 单条消息发送时间 < 5秒
- 支持同时管理 > 100个电话号码
- 发送成功率 > 95%

### 用户体验指标
- 界面响应时间 < 1秒
- 操作流程直观易懂
- 错误提示清晰明确
- 状态反馈及时准确
