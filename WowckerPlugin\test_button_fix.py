#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮修复的脚本
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('button_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def test_ui_component_access():
    """测试UI组件访问"""
    try:
        # 模拟主窗口结构
        class MockMainWindow:
            def __init__(self):
                self.current_username = 'test_user'
                self.ui = MockUI()
        
        class MockUI:
            def __init__(self):
                self.load_pages = MockLoadPages()
        
        class MockLoadPages:
            def __init__(self):
                # 导入真实的Qt组件
                try:
                    from PySide6.QtWidgets import QWidget, QPushButton, QLabel, QTextEdit, QTableWidget
                    from PySide6.QtCore import QObject
                    
                    # 创建真实的UI组件
                    self.message_input_field = QTextEdit()
                    self.phone_numbers_table = QTableWidget()
                    self.bulk_send_btn = QPushButton("群发消息")
                    self.add_phone_btn = QPushButton("添加号码")
                    self.login_status_indicator = QLabel("●")
                    self.login_status_text = QLabel("未登录")
                    self.login_info_text = QLabel("请先登录WhatsApp才能使用群发功能")
                    self.whatsapp_login_btn = QPushButton("登录WhatsApp")
                    self.whatsapp_logout_btn = QPushButton("退出登录")
                    self.send_status_label = QLabel("请先登录WhatsApp并输入消息")
                    
                    logger.info("✓ 成功创建所有UI组件")
                    
                except ImportError as e:
                    logger.error(f"✗ 无法导入PySide6: {e}")
                    # 创建模拟组件
                    class MockComponent:
                        def __init__(self, name):
                            self.name = name
                            self.clicked = MockSignal()
                            self.textChanged = MockSignal()
                        
                        def isEnabled(self):
                            return True
                            
                        def isVisible(self):
                            return True
                            
                        def setEnabled(self, enabled):
                            pass
                            
                        def setText(self, text):
                            pass
                            
                        def setPlaceholderText(self, text):
                            pass
                    
                    class MockSignal:
                        def connect(self, slot):
                            logger.info(f"模拟信号连接: {slot}")
                    
                    self.message_input_field = MockComponent("message_input_field")
                    self.phone_numbers_table = MockComponent("phone_numbers_table")
                    self.bulk_send_btn = MockComponent("bulk_send_btn")
                    self.add_phone_btn = MockComponent("add_phone_btn")
                    self.login_status_indicator = MockComponent("login_status_indicator")
                    self.login_status_text = MockComponent("login_status_text")
                    self.login_info_text = MockComponent("login_info_text")
                    self.whatsapp_login_btn = MockComponent("whatsapp_login_btn")
                    self.whatsapp_logout_btn = MockComponent("whatsapp_logout_btn")
                    self.send_status_label = MockComponent("send_status_label")
                    
                    logger.info("✓ 创建了模拟UI组件")
        
        # 测试控制器初始化
        mock_window = MockMainWindow()
        
        # 导入控制器
        sys.path.append(str(project_root / "PyOneDark_Qt_Widgets_Modern_GUI-master"))
        from gui.core.bulk_customer_acquisition_controller import BulkCustomerAcquisitionController
        
        logger.info("开始初始化控制器...")
        controller = BulkCustomerAcquisitionController(mock_window)
        logger.info("✓ 控制器初始化成功")
        
        # 测试按钮方法调用
        logger.info("测试按钮方法调用...")
        
        # 测试添加电话号码方法
        logger.info("测试添加电话号码方法...")
        try:
            # 这里不会真正弹出对话框，因为我们没有真实的GUI环境
            # 但可以检查方法是否存在和可调用
            if hasattr(controller, 'add_phone_number_dialog'):
                logger.info("✓ add_phone_number_dialog 方法存在")
            else:
                logger.error("✗ add_phone_number_dialog 方法不存在")
        except Exception as e:
            logger.error(f"✗ 测试添加电话号码方法时出错: {e}")
        
        # 测试WhatsApp登录方法
        logger.info("测试WhatsApp登录方法...")
        try:
            if hasattr(controller, 'start_whatsapp_login'):
                logger.info("✓ start_whatsapp_login 方法存在")
            else:
                logger.error("✗ start_whatsapp_login 方法不存在")
        except Exception as e:
            logger.error(f"✗ 测试WhatsApp登录方法时出错: {e}")
        
        return True
        
    except Exception as e:
        logger.exception(f"测试UI组件访问时出错: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试按钮修复...")
    logger.info("=" * 60)
    
    try:
        success = test_ui_component_access()
        if success:
            logger.info("✓ 测试完成，按钮连接应该已经修复")
        else:
            logger.error("✗ 测试失败，可能仍有问题")
    except Exception as e:
        logger.exception(f"测试过程中出错: {e}")
    
    logger.info("=" * 60)
    logger.info("测试结束")

if __name__ == "__main__":
    main()
