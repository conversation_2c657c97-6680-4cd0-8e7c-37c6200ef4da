"""
电话号码数据库操作类
负责管理群发获客功能的电话号码数据
"""

import logging
from typing import Dict, List, Optional, Any
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class TeleNumberOperations:
    """电话号码数据库操作类"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化电话号码操作类
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager or DatabaseManager()
    
    def add_phone_number(self, phone_number: str, username: str, store_name: str = None) -> int:
        """
        添加电话号码到数据库
        
        Args:
            phone_number: 电话号码（已格式化）
            username: 用户名
            store_name: 店铺名称（可选）
            
        Returns:
            int: 新记录ID，失败返回-1
        """
        # 检查号码是否已存在
        existing = self.get_phone_number(phone_number, username, store_name)
        if existing:
            logger.warning(f"电话号码已存在: {phone_number} (用户: {username})")
            return -1
        
        query = """
        INSERT INTO telenumber (phone_number, username, store_name, status)
        VALUES (?, ?, ?, 'pending')
        """
        
        if not self.db_manager.connect():
            return -1
        
        try:
            self.db_manager.cursor.execute(query, (phone_number, username, store_name))
            self.db_manager.conn.commit()
            record_id = self.db_manager.get_last_insert_id()
            logger.info(f"添加电话号码成功: {phone_number} (ID: {record_id})")
            return record_id
        except Exception as e:
            logger.error(f"添加电话号码时出错: {str(e)}")
            self.db_manager.conn.rollback()
            return -1
        finally:
            self.db_manager.close()
    
    def get_phone_number(self, phone_number: str, username: str, store_name: str = None) -> Optional[Dict]:
        """
        获取特定的电话号码记录
        
        Args:
            phone_number: 电话号码
            username: 用户名
            store_name: 店铺名称（可选）
            
        Returns:
            Optional[Dict]: 电话号码记录
        """
        query = """
        SELECT id, phone_number, username, store_name, status, created_at, updated_at
        FROM telenumber
        WHERE phone_number = ? AND username = ? AND (store_name = ? OR (store_name IS NULL AND ? IS NULL))
        """
        
        results = self.db_manager.execute_query(query, (phone_number, username, store_name, store_name))
        if not results:
            return None
        
        return self._row_to_dict(results[0])
    
    def get_phone_numbers_by_user(self, username: str, store_name: str = None) -> List[Dict]:
        """
        获取用户的所有电话号码
        
        Args:
            username: 用户名
            store_name: 店铺名称（可选，为None时获取所有店铺的号码）
            
        Returns:
            List[Dict]: 电话号码列表
        """
        if store_name is None:
            query = """
            SELECT id, phone_number, username, store_name, status, created_at, updated_at
            FROM telenumber
            WHERE username = ?
            ORDER BY created_at DESC
            """
            params = (username,)
        else:
            query = """
            SELECT id, phone_number, username, store_name, status, created_at, updated_at
            FROM telenumber
            WHERE username = ? AND (store_name = ? OR (store_name IS NULL AND ? IS NULL))
            ORDER BY created_at DESC
            """
            params = (username, store_name, store_name)
        
        results = self.db_manager.execute_query(query, params)
        return [self._row_to_dict(row) for row in results]
    
    def update_phone_status(self, phone_id: int, status: str) -> bool:
        """
        更新电话号码状态
        
        Args:
            phone_id: 电话号码记录ID
            status: 新状态 (pending/sending/sent/failed/blocked)
            
        Returns:
            bool: 更新是否成功
        """
        query = """
        UPDATE telenumber 
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        
        success = self.db_manager.execute_update(query, (status, phone_id))
        if success:
            logger.info(f"更新电话号码状态成功: ID {phone_id} -> {status}")
        else:
            logger.error(f"更新电话号码状态失败: ID {phone_id}")
        
        return success
    
    def update_phone_status_by_number(self, phone_number: str, username: str, status: str, store_name: str = None) -> bool:
        """
        通过电话号码更新状态
        
        Args:
            phone_number: 电话号码
            username: 用户名
            status: 新状态
            store_name: 店铺名称（可选）
            
        Returns:
            bool: 更新是否成功
        """
        query = """
        UPDATE telenumber 
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE phone_number = ? AND username = ? AND (store_name = ? OR (store_name IS NULL AND ? IS NULL))
        """
        
        success = self.db_manager.execute_update(query, (status, phone_number, username, store_name, store_name))
        if success:
            logger.info(f"更新电话号码状态成功: {phone_number} -> {status}")
        else:
            logger.error(f"更新电话号码状态失败: {phone_number}")
        
        return success
    
    def delete_phone_number(self, phone_id: int) -> bool:
        """
        删除电话号码记录
        
        Args:
            phone_id: 电话号码记录ID
            
        Returns:
            bool: 删除是否成功
        """
        query = "DELETE FROM telenumber WHERE id = ?"
        
        success = self.db_manager.execute_update(query, (phone_id,))
        if success:
            logger.info(f"删除电话号码成功: ID {phone_id}")
        else:
            logger.error(f"删除电话号码失败: ID {phone_id}")
        
        return success
    
    def delete_phone_number_by_number(self, phone_number: str, username: str, store_name: str = None) -> bool:
        """
        通过电话号码删除记录
        
        Args:
            phone_number: 电话号码
            username: 用户名
            store_name: 店铺名称（可选）
            
        Returns:
            bool: 删除是否成功
        """
        query = """
        DELETE FROM telenumber 
        WHERE phone_number = ? AND username = ? AND (store_name = ? OR (store_name IS NULL AND ? IS NULL))
        """
        
        success = self.db_manager.execute_update(query, (phone_number, username, store_name, store_name))
        if success:
            logger.info(f"删除电话号码成功: {phone_number}")
        else:
            logger.error(f"删除电话号码失败: {phone_number}")
        
        return success
    
    def clear_phone_numbers(self, username: str, store_name: str = None) -> bool:
        """
        清空用户的所有电话号码
        
        Args:
            username: 用户名
            store_name: 店铺名称（可选，为None时清空所有店铺的号码）
            
        Returns:
            bool: 清空是否成功
        """
        if store_name is None:
            query = "DELETE FROM telenumber WHERE username = ?"
            params = (username,)
        else:
            query = """
            DELETE FROM telenumber 
            WHERE username = ? AND (store_name = ? OR (store_name IS NULL AND ? IS NULL))
            """
            params = (username, store_name, store_name)
        
        success = self.db_manager.execute_update(query, params)
        if success:
            logger.info(f"清空电话号码成功: 用户 {username}")
        else:
            logger.error(f"清空电话号码失败: 用户 {username}")
        
        return success
    
    def get_phone_count_by_status(self, username: str, status: str, store_name: str = None) -> int:
        """
        获取指定状态的电话号码数量
        
        Args:
            username: 用户名
            status: 状态
            store_name: 店铺名称（可选）
            
        Returns:
            int: 电话号码数量
        """
        if store_name is None:
            query = """
            SELECT COUNT(*) FROM telenumber 
            WHERE username = ? AND status = ?
            """
            params = (username, status)
        else:
            query = """
            SELECT COUNT(*) FROM telenumber 
            WHERE username = ? AND status = ? AND (store_name = ? OR (store_name IS NULL AND ? IS NULL))
            """
            params = (username, status, store_name, store_name)
        
        results = self.db_manager.execute_query(query, params)
        return results[0][0] if results else 0
    
    def _row_to_dict(self, row) -> Dict:
        """
        将数据库行转换为字典
        
        Args:
            row: 数据库行
            
        Returns:
            Dict: 转换后的字典
        """
        return {
            'id': row[0],
            'phone_number': row[1],
            'username': row[2],
            'store_name': row[3],
            'status': row[4],
            'created_at': row[5],
            'updated_at': row[6]
        }
    
    def get_statistics(self, username: str, store_name: str = None) -> Dict:
        """
        获取电话号码统计信息
        
        Args:
            username: 用户名
            store_name: 店铺名称（可选）
            
        Returns:
            Dict: 统计信息
        """
        stats = {
            'total': 0,
            'pending': 0,
            'sending': 0,
            'sent': 0,
            'failed': 0,
            'blocked': 0
        }
        
        # 获取总数
        stats['total'] = len(self.get_phone_numbers_by_user(username, store_name))
        
        # 获取各状态数量
        for status in ['pending', 'sending', 'sent', 'failed', 'blocked']:
            stats[status] = self.get_phone_count_by_status(username, status, store_name)
        
        return stats
