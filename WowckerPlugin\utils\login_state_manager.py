# -*- coding: utf-8 -*-

"""
登录状态管理器
负责WhatsApp登录状态的持久化和跨页面管理
"""

import os
import json
import time
import logging
from typing import Dict, Any, Optional, Callable
from pathlib import Path

logger = logging.getLogger(__name__)

class LoginStateManager:
    """登录状态管理器"""
    
    def __init__(self, username: str = "default_user", store_name: str = "bulk_messaging"):
        """
        初始化登录状态管理器
        
        Args:
            username: 用户名
            store_name: 店铺名称
        """
        self.username = username
        self.store_name = store_name
        
        # 创建状态存储目录
        self.state_dir = Path(__file__).parent.parent / "data" / "login_states"
        self.state_dir.mkdir(parents=True, exist_ok=True)
        
        # 状态文件路径
        safe_username = "".join([c for c in username if c.isalnum() or c in "_-"])
        safe_storename = "".join([c for c in store_name if c.isalnum() or c in "_-"])
        state_filename = f"{safe_username}_{safe_storename}_login_state.json"
        self.state_file = self.state_dir / state_filename
        
        # 状态变化回调
        self.status_callbacks = []
        
        logger.info(f"初始化登录状态管理器: {username}@{store_name}")
    
    def add_status_callback(self, callback: Callable[[bool], None]):
        """
        添加状态变化回调函数
        
        Args:
            callback: 回调函数，参数为登录状态(bool)
        """
        if callback not in self.status_callbacks:
            self.status_callbacks.append(callback)
    
    def remove_status_callback(self, callback: Callable[[bool], None]):
        """
        移除状态变化回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        if callback in self.status_callbacks:
            self.status_callbacks.remove(callback)
    
    def _notify_status_change(self, is_logged_in: bool):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(is_logged_in)
            except Exception as e:
                logger.error(f"状态回调执行失败: {e}")
    
    def save_login_state(self, is_logged_in: bool, session_info: Dict[str, Any] = None):
        """
        保存登录状态
        
        Args:
            is_logged_in: 是否已登录
            session_info: 会话信息
        """
        try:
            state_data = {
                "username": self.username,
                "store_name": self.store_name,
                "is_logged_in": is_logged_in,
                "last_update": time.time(),
                "session_info": session_info or {}
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"登录状态已保存: {is_logged_in}")
            
            # 通知状态变化
            self._notify_status_change(is_logged_in)
            
        except Exception as e:
            logger.error(f"保存登录状态失败: {e}")
    
    def load_login_state(self) -> Dict[str, Any]:
        """
        加载登录状态
        
        Returns:
            Dict[str, Any]: 登录状态信息
        """
        try:
            if not self.state_file.exists():
                return self._get_default_state()
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 检查状态文件是否过期（超过24小时）
            last_update = state_data.get("last_update", 0)
            if time.time() - last_update > 24 * 3600:
                logger.warning("登录状态文件已过期，重置为未登录状态")
                return self._get_default_state()
            
            # 验证用户名和店铺名
            if (state_data.get("username") != self.username or 
                state_data.get("store_name") != self.store_name):
                logger.warning("登录状态文件用户信息不匹配，重置状态")
                return self._get_default_state()
            
            logger.info(f"加载登录状态: {state_data.get('is_logged_in', False)}")
            return state_data
            
        except Exception as e:
            logger.error(f"加载登录状态失败: {e}")
            return self._get_default_state()
    
    def _get_default_state(self) -> Dict[str, Any]:
        """获取默认状态"""
        return {
            "username": self.username,
            "store_name": self.store_name,
            "is_logged_in": False,
            "last_update": time.time(),
            "session_info": {}
        }
    
    def is_logged_in(self) -> bool:
        """
        检查是否已登录
        
        Returns:
            bool: 是否已登录
        """
        state = self.load_login_state()
        return state.get("is_logged_in", False)
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        获取会话信息
        
        Returns:
            Dict[str, Any]: 会话信息
        """
        state = self.load_login_state()
        return state.get("session_info", {})
    
    def clear_login_state(self):
        """清除登录状态"""
        try:
            if self.state_file.exists():
                self.state_file.unlink()
            logger.info("登录状态已清除")
            
            # 通知状态变化
            self._notify_status_change(False)
            
        except Exception as e:
            logger.error(f"清除登录状态失败: {e}")
    
    def update_session_info(self, session_info: Dict[str, Any]):
        """
        更新会话信息（保持登录状态不变）
        
        Args:
            session_info: 新的会话信息
        """
        try:
            current_state = self.load_login_state()
            current_state["session_info"].update(session_info)
            current_state["last_update"] = time.time()
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(current_state, f, ensure_ascii=False, indent=2)
            
            logger.info("会话信息已更新")
            
        except Exception as e:
            logger.error(f"更新会话信息失败: {e}")
    
    def get_last_update_time(self) -> float:
        """
        获取最后更新时间
        
        Returns:
            float: 时间戳
        """
        state = self.load_login_state()
        return state.get("last_update", 0)
    
    def is_state_expired(self, expire_hours: int = 24) -> bool:
        """
        检查状态是否过期
        
        Args:
            expire_hours: 过期时间（小时）
            
        Returns:
            bool: 是否过期
        """
        last_update = self.get_last_update_time()
        return time.time() - last_update > expire_hours * 3600
    
    def refresh_state(self):
        """刷新状态时间戳"""
        try:
            current_state = self.load_login_state()
            current_state["last_update"] = time.time()
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(current_state, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"刷新状态失败: {e}")
    
    @classmethod
    def get_all_login_states(cls, data_dir: Path = None) -> Dict[str, Dict[str, Any]]:
        """
        获取所有用户的登录状态
        
        Args:
            data_dir: 数据目录路径
            
        Returns:
            Dict[str, Dict[str, Any]]: 所有登录状态
        """
        if data_dir is None:
            data_dir = Path(__file__).parent.parent / "data" / "login_states"
        
        states = {}
        
        try:
            if not data_dir.exists():
                return states
            
            for state_file in data_dir.glob("*_login_state.json"):
                try:
                    with open(state_file, 'r', encoding='utf-8') as f:
                        state_data = json.load(f)
                    
                    username = state_data.get("username", "unknown")
                    store_name = state_data.get("store_name", "unknown")
                    key = f"{username}@{store_name}"
                    states[key] = state_data
                    
                except Exception as e:
                    logger.error(f"读取状态文件失败 {state_file}: {e}")
            
        except Exception as e:
            logger.error(f"获取所有登录状态失败: {e}")
        
        return states
    
    @classmethod
    def cleanup_expired_states(cls, expire_hours: int = 24, data_dir: Path = None):
        """
        清理过期的登录状态文件
        
        Args:
            expire_hours: 过期时间（小时）
            data_dir: 数据目录路径
        """
        if data_dir is None:
            data_dir = Path(__file__).parent.parent / "data" / "login_states"
        
        try:
            if not data_dir.exists():
                return
            
            current_time = time.time()
            expire_threshold = expire_hours * 3600
            
            for state_file in data_dir.glob("*_login_state.json"):
                try:
                    with open(state_file, 'r', encoding='utf-8') as f:
                        state_data = json.load(f)
                    
                    last_update = state_data.get("last_update", 0)
                    if current_time - last_update > expire_threshold:
                        state_file.unlink()
                        logger.info(f"已清理过期状态文件: {state_file.name}")
                
                except Exception as e:
                    logger.error(f"清理状态文件失败 {state_file}: {e}")
        
        except Exception as e:
            logger.error(f"清理过期状态失败: {e}")
