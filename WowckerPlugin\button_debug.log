2025-06-21 15:28:05,029 - __main__ - INFO - 开始全面诊断群发获客按钮无响应问题...
2025-06-21 15:28:05,030 - __main__ - INFO - ================================================================================
2025-06-21 15:28:05,030 - __main__ - INFO - === 分析控制器初始化顺序 ===
2025-06-21 15:28:05,030 - __main__ - INFO - 控制器初始化顺序:
2025-06-21 15:28:05,030 - __main__ - INFO -   第34行: from gui.core.ai_service_controller import AIServiceController
2025-06-21 15:28:05,031 - __main__ - INFO -   第36行: from gui.core.intent_analysis_controller import IntentAnalysisController
2025-06-21 15:28:05,031 - __main__ - INFO -   第78行: self.ai_service_controller = AIServiceController(self)
2025-06-21 15:28:05,031 - __main__ - INFO -   第81行: self.intent_analysis_controller = IntentAnalysisController(self)
2025-06-21 15:28:05,031 - __main__ - INFO -   第84行: from gui.core.bulk_customer_acquisition_controller import BulkCustomerAcquisitionController
2025-06-21 15:28:05,031 - __main__ - INFO -   第85行: self.bulk_acquisition_controller = BulkCustomerAcquisitionController(self)
2025-06-21 15:28:05,031 - __main__ - WARNING - ✗ 主窗口缺少异常处理
2025-06-21 15:28:05,031 - __main__ - INFO - 
2025-06-21 15:28:05,031 - __main__ - INFO - === 检查UI组件冲突 ===
2025-06-21 15:28:05,032 - __main__ - INFO - ✓ 群发组件存在: add_phone_btn
2025-06-21 15:28:05,032 - __main__ - INFO - ✓ 群发组件存在: whatsapp_login_btn
2025-06-21 15:28:05,032 - __main__ - INFO - ✓ 群发组件存在: bulk_send_btn
2025-06-21 15:28:05,033 - __main__ - INFO - ✓ 群发组件存在: login_status_indicator
2025-06-21 15:28:05,033 - __main__ - INFO - ✓ 群发组件存在: login_status_text
2025-06-21 15:28:05,033 - __main__ - INFO - ✓ 群发组件存在: message_input_field
2025-06-21 15:28:05,033 - __main__ - INFO - ✓ 群发组件存在: phone_numbers_table
2025-06-21 15:28:05,034 - __main__ - WARNING - ⚠ AI客服组件缺失: status_text (这可能导致初始化警告)
2025-06-21 15:28:05,034 - __main__ - WARNING - ⚠ AI客服组件缺失: ai_service_frame (这可能导致初始化警告)
2025-06-21 15:28:05,034 - __main__ - INFO - 
2025-06-21 15:28:05,034 - __main__ - INFO - === 追踪信号连接链 ===
2025-06-21 15:28:05,035 - __main__ - INFO - ✓ 方法存在: def __init__(self, main_window):
2025-06-21 15:28:05,035 - __main__ - INFO - ✓ 方法存在: def _initialize_ui_components(self):
2025-06-21 15:28:05,035 - __main__ - INFO - ✓ 方法存在: def connect_signals(self):
2025-06-21 15:28:05,035 - __main__ - INFO - ✓ 方法存在: def add_phone_number_dialog(self):
2025-06-21 15:28:05,035 - __main__ - INFO - ✓ 方法存在: def start_whatsapp_login(self):
2025-06-21 15:28:05,035 - __main__ - INFO - ✓ 信号连接存在: self.add_phone_btn.clicked.connect(self.add_phone_number_dialog)
2025-06-21 15:28:05,036 - __main__ - INFO - ✓ 信号连接存在: self.whatsapp_login_btn.clicked.connect(self.start_whatsapp_login)
2025-06-21 15:28:05,036 - __main__ - INFO - ✓ 信号连接存在: self.bulk_send_btn.clicked.connect(self.start_bulk_send)
2025-06-21 15:28:05,036 - __main__ - INFO - ✓ 控制器有详细的错误处理和日志
2025-06-21 15:28:05,036 - __main__ - INFO - 
2025-06-21 15:28:05,036 - __main__ - INFO - === 检查WhatsApp登录JS文件 ===
2025-06-21 15:28:05,037 - __main__ - INFO - ✓ JS功能存在: whatsapp-web.js
2025-06-21 15:28:05,037 - __main__ - INFO - ✓ JS功能存在: qrcode
2025-06-21 15:28:05,037 - __main__ - INFO - ✓ JS功能存在: ready
2025-06-21 15:28:05,037 - __main__ - INFO - ✓ JS功能存在: authenticated
2025-06-21 15:28:05,037 - __main__ - INFO - ✓ WhatsApp登录JS文件存在，大小: 8630 字符
2025-06-21 15:28:05,037 - __main__ - INFO - 
2025-06-21 15:28:05,037 - __main__ - INFO - === 分析潜在问题 ===
2025-06-21 15:28:05,037 - __main__ - INFO - 1. AI客服控制器警告分析:
2025-06-21 15:28:05,037 - __main__ - INFO -    - AI客服控制器寻找不存在的UI组件 (status_text, ai_service_frame)
2025-06-21 15:28:05,038 - __main__ - INFO -    - 这些警告不应该影响群发控制器的功能
2025-06-21 15:28:05,038 - __main__ - INFO -    - 但可能表明UI初始化过程中有问题
2025-06-21 15:28:05,038 - __main__ - INFO - 2. 初始化顺序分析:
2025-06-21 15:28:05,038 - __main__ - INFO -    - AI客服控制器在第78行初始化
2025-06-21 15:28:05,038 - __main__ - INFO -    - 群发获客控制器在第84-85行初始化
2025-06-21 15:28:05,038 - __main__ - INFO -    - 群发控制器在AI控制器之后初始化，应该没问题
2025-06-21 15:28:05,038 - __main__ - INFO - 3. 可能的异常原因:
2025-06-21 15:28:05,038 - __main__ - INFO -    - UI组件引用失败
2025-06-21 15:28:05,039 - __main__ - INFO -    - 信号连接过程中的异常
2025-06-21 15:28:05,039 - __main__ - INFO -    - 控制器初始化过程中的异常
2025-06-21 15:28:05,039 - __main__ - INFO -    - Qt事件循环问题
2025-06-21 15:28:05,039 - __main__ - INFO - 
2025-06-21 15:28:05,039 - __main__ - INFO - === 修复建议 ===
2025-06-21 15:28:05,039 - __main__ - INFO -   1. 在AI客服控制器中移除对不存在UI组件的检查
2025-06-21 15:28:05,039 - __main__ - INFO -   2. 在群发控制器初始化时添加更详细的日志
2025-06-21 15:28:05,039 - __main__ - INFO -   3. 在主窗口初始化时添加异常处理
2025-06-21 15:28:05,039 - __main__ - INFO -   4. 确保UI组件完全创建后再初始化控制器
2025-06-21 15:28:05,040 - __main__ - INFO -   5. 添加按钮点击事件的调试日志
2025-06-21 15:28:05,040 - __main__ - INFO - ================================================================================
2025-06-21 15:28:05,040 - __main__ - INFO - 诊断完成
2025-06-21 15:28:05,040 - __main__ - INFO - ✓ UI组件检查通过
2025-06-21 15:28:05,040 - __main__ - INFO - 生成了 5 条修复建议
2025-06-21 15:28:36,724 - __main__ - INFO - 开始全面诊断群发获客按钮无响应问题...
2025-06-21 15:28:36,725 - __main__ - INFO - ================================================================================
2025-06-21 15:28:36,725 - __main__ - INFO - === 分析控制器初始化顺序 ===
2025-06-21 15:28:36,725 - __main__ - INFO - 控制器初始化顺序:
2025-06-21 15:28:36,726 - __main__ - INFO -   第34行: from gui.core.ai_service_controller import AIServiceController
2025-06-21 15:28:36,726 - __main__ - INFO -   第36行: from gui.core.intent_analysis_controller import IntentAnalysisController
2025-06-21 15:28:36,726 - __main__ - INFO -   第81行: self.ai_service_controller = AIServiceController(self)
2025-06-21 15:28:36,726 - __main__ - INFO -   第91行: self.intent_analysis_controller = IntentAnalysisController(self)
2025-06-21 15:28:36,726 - __main__ - INFO -   第101行: from gui.core.bulk_customer_acquisition_controller import BulkCustomerAcquisitionController
2025-06-21 15:28:36,726 - __main__ - INFO -   第102行: self.bulk_acquisition_controller = BulkCustomerAcquisitionController(self)
2025-06-21 15:28:36,726 - __main__ - INFO - ✓ 主窗口有异常处理
2025-06-21 15:28:36,726 - __main__ - INFO - 
2025-06-21 15:28:36,726 - __main__ - INFO - === 检查UI组件冲突 ===
2025-06-21 15:28:36,727 - __main__ - INFO - ✓ 群发组件存在: add_phone_btn
2025-06-21 15:28:36,727 - __main__ - INFO - ✓ 群发组件存在: whatsapp_login_btn
2025-06-21 15:28:36,727 - __main__ - INFO - ✓ 群发组件存在: bulk_send_btn
2025-06-21 15:28:36,727 - __main__ - INFO - ✓ 群发组件存在: login_status_indicator
2025-06-21 15:28:36,727 - __main__ - INFO - ✓ 群发组件存在: login_status_text
2025-06-21 15:28:36,728 - __main__ - INFO - ✓ 群发组件存在: message_input_field
2025-06-21 15:28:36,728 - __main__ - INFO - ✓ 群发组件存在: phone_numbers_table
2025-06-21 15:28:36,728 - __main__ - WARNING - ⚠ AI客服组件缺失: status_text (这可能导致初始化警告)
2025-06-21 15:28:36,728 - __main__ - WARNING - ⚠ AI客服组件缺失: ai_service_frame (这可能导致初始化警告)
2025-06-21 15:28:36,728 - __main__ - INFO - 
2025-06-21 15:28:36,728 - __main__ - INFO - === 追踪信号连接链 ===
2025-06-21 15:28:36,730 - __main__ - INFO - ✓ 方法存在: def __init__(self, main_window):
2025-06-21 15:28:36,730 - __main__ - INFO - ✓ 方法存在: def _initialize_ui_components(self):
2025-06-21 15:28:36,730 - __main__ - INFO - ✓ 方法存在: def connect_signals(self):
2025-06-21 15:28:36,730 - __main__ - INFO - ✓ 方法存在: def add_phone_number_dialog(self):
2025-06-21 15:28:36,730 - __main__ - INFO - ✓ 方法存在: def start_whatsapp_login(self):
2025-06-21 15:28:36,730 - __main__ - INFO - ✓ 信号连接存在: self.add_phone_btn.clicked.connect(self.add_phone_number_dialog)
2025-06-21 15:28:36,730 - __main__ - INFO - ✓ 信号连接存在: self.whatsapp_login_btn.clicked.connect(self.start_whatsapp_login)
2025-06-21 15:28:36,731 - __main__ - INFO - ✓ 信号连接存在: self.bulk_send_btn.clicked.connect(self.start_bulk_send)
2025-06-21 15:28:36,731 - __main__ - INFO - ✓ 控制器有详细的错误处理和日志
2025-06-21 15:28:36,731 - __main__ - INFO - 
2025-06-21 15:28:36,731 - __main__ - INFO - === 检查WhatsApp登录JS文件 ===
2025-06-21 15:28:36,731 - __main__ - INFO - ✓ JS功能存在: whatsapp-web.js
2025-06-21 15:28:36,731 - __main__ - INFO - ✓ JS功能存在: qrcode
2025-06-21 15:28:36,732 - __main__ - INFO - ✓ JS功能存在: ready
2025-06-21 15:28:36,732 - __main__ - INFO - ✓ JS功能存在: authenticated
2025-06-21 15:28:36,732 - __main__ - INFO - ✓ WhatsApp登录JS文件存在，大小: 8630 字符
2025-06-21 15:28:36,732 - __main__ - INFO - 
2025-06-21 15:28:36,732 - __main__ - INFO - === 分析潜在问题 ===
2025-06-21 15:28:36,732 - __main__ - INFO - 1. AI客服控制器警告分析:
2025-06-21 15:28:36,732 - __main__ - INFO -    - AI客服控制器寻找不存在的UI组件 (status_text, ai_service_frame)
2025-06-21 15:28:36,732 - __main__ - INFO -    - 这些警告不应该影响群发控制器的功能
2025-06-21 15:28:36,732 - __main__ - INFO -    - 但可能表明UI初始化过程中有问题
2025-06-21 15:28:36,732 - __main__ - INFO - 2. 初始化顺序分析:
2025-06-21 15:28:36,732 - __main__ - INFO -    - AI客服控制器在第78行初始化
2025-06-21 15:28:36,732 - __main__ - INFO -    - 群发获客控制器在第84-85行初始化
2025-06-21 15:28:36,733 - __main__ - INFO -    - 群发控制器在AI控制器之后初始化，应该没问题
2025-06-21 15:28:36,733 - __main__ - INFO - 3. 可能的异常原因:
2025-06-21 15:28:36,733 - __main__ - INFO -    - UI组件引用失败
2025-06-21 15:28:36,733 - __main__ - INFO -    - 信号连接过程中的异常
2025-06-21 15:28:36,733 - __main__ - INFO -    - 控制器初始化过程中的异常
2025-06-21 15:28:36,733 - __main__ - INFO -    - Qt事件循环问题
2025-06-21 15:28:36,733 - __main__ - INFO - 
2025-06-21 15:28:36,733 - __main__ - INFO - === 修复建议 ===
2025-06-21 15:28:36,733 - __main__ - INFO -   1. 在AI客服控制器中移除对不存在UI组件的检查
2025-06-21 15:28:36,733 - __main__ - INFO -   2. 在群发控制器初始化时添加更详细的日志
2025-06-21 15:28:36,734 - __main__ - INFO -   3. 在主窗口初始化时添加异常处理
2025-06-21 15:28:36,734 - __main__ - INFO -   4. 确保UI组件完全创建后再初始化控制器
2025-06-21 15:28:36,734 - __main__ - INFO -   5. 添加按钮点击事件的调试日志
2025-06-21 15:28:36,734 - __main__ - INFO - ================================================================================
2025-06-21 15:28:36,734 - __main__ - INFO - 诊断完成
2025-06-21 15:28:36,734 - __main__ - INFO - ✓ UI组件检查通过
2025-06-21 15:28:36,734 - __main__ - INFO - 生成了 5 条修复建议
